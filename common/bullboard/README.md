# Bullboard

---

BullBoard is a queue monitoring tool for BullMQ that provides an intuitive interface to view and manage queues. This application is integrated with Google SSO for secure authentication, allowing users with appropriate permissions to perform write operations on the queues.

---
**Folder Struture**

---

```
├── common
    └── bullboard
        └── kustomize
            └── base
                ├── deployment.yaml
                ├── ingress.yaml
                └── service.yaml
```

---

## Guidelines (Refer [RFC 2119](https://www.ietf.org/rfc/rfc2119.txt))

---

- You **SHOULD** follow the **Conventional Commits** - [Refer to this document](https://www.conventionalcommits.org/en/v1.0.0/)  
- You **MUST** use only lowercase for file and folder names.  
    - For files: You **MUST NOT** use spaces. Instead, use the underscore symbol (`_`) followed by the file extension.  
    - For folders: You **MUST NOT** use spaces. Use only the hyphen symbol (`-`) instead.  
- In overlays folders, you **SHOULD** name files generically to support other services and environments, minimizing pipeline reusability effort for newer 
  services.  
- Each file name in the overlays folder **MUST** be self-explanatory and easy for developers to understand and modify resources in their environment.  
- You **SHOULD** update the folder structure in the `README.md` file if you have modified any file names or added new files.
- The files inside `base` folder **SHOULD NOT** be fully functional context files. 
- Developers **MUST NOT** change any structure in staging overlay. You can include placeholder values in areas that need to be overridden for staging and production environments.

---

## K8s Contexts File

---

The folder structure **SHOULD** consists of two main components: the **base** folder and the **overlays** folder. The base folder contains all the common context configurations and acts as a template for reusable settings. Files in this folder **SHOULD NOT** be fully functional context files; instead, they should include placeholder values for parameters that need to be overridden in staging and production environments. Each resource type (kind) will have its own dedicated context file within the base folder.

<!--- NOT APPLICABLE TO THIS SERVICE
The overlays folder is divided into two subfolders: `staging` and `production`. ~~The `staging` folder basically allows developers to modify resource values specific to the staging environment. Developers **MUST** only edit files within this folder and avoid modifying the `kustomization.yaml` file unless absolutely necessary. The `production` folder, on the other hand, is used for overrides specific to the production environment and inherits configurations from the base folder. This structure ensures a clear separation between common configurations and environment-specific customizations, promoting better organization and maintainability.  
-->