apiVersion: apps/v1
kind: Deployment
metadata:
  name: bullboard
  labels:
    app: bullboard
spec:
  replicas: 1
  revisionHistoryLimit: 1
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: bullboard
      application: bullboard
  template:
    metadata:
      labels:
        app: bullboard
        application: bullboard
    spec:
      terminationGracePeriodSeconds: 30
      containers:
      - name: bullboard
        image: ************.dkr.ecr.us-east-1.amazonaws.com/bullboard:latest # patch this with the respective account's ECR image URL
        imagePullPolicy: Always
        resources:
          requests:
            cpu: 500m
            memory: 512Mi
          limits:
            cpu: 1000m
            memory: 1000Mi
        readinessProbe:
          httpGet:
            path: /status
            port: 3000
          initialDelaySeconds: 20
          timeoutSeconds: 10
          periodSeconds: 30
          failureThreshold: 3
          successThreshold: 1
        livenessProbe:
          httpGet:
            path: /status
            port: 3000
          initialDelaySeconds: 30
          timeoutSeconds: 10
          periodSeconds: 60
          failureThreshold: 3
          successThreshold: 1
        ports:
        - containerPort: 3000
        envFrom:
          - configMapRef:
              name: <config-from-remote-repo> # Placeholder for configmap to patch in the overlays
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
      volumes:
      - name: config-volume
        configMap:
          name: <config-from-remote-repo> # Placeholder for confirmap to patch in the overlays
          items:
          - key: config
            path: <staging.json/production.json> # Placeholder for configmap to patch in the overlays
