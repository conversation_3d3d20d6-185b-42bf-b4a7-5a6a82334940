apiVersion: apps/v1
kind: Deployment
metadata:
  name: sparrowconnect
  labels:
    app: sparrowconnect
spec:
  selector:
    matchLabels:
      app: sparrowconnect
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: sparrowconnect
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
      containers:
      - name: sparrowconnect
        envFrom:
        - configMapRef:
            name: sparrowconnect-env
        image: sparrowconnect:latest # change to the correct image based on the region and account
        imagePullPolicy: IfNotPresent
        volumeMounts:
        - name: config-mount
          mountPath: /app/config.json
          subPath: config.json
        securityContext:
          allowPrivilegeEscalation: false
          runAsNonRoot: true
          seccompProfile:
            type: RuntimeDefault
        ports:
          - containerPort: 2775
            protocol: TCP
        resources:
          requests:
            cpu: 500m
            memory: 300Mi
          limits:
            memory: 500Mi
      terminationGracePeriodSeconds: 60
      volumes:
      - name: config-mount
        configMap:
          name: sparrowconnect-config
          items:
          - key: config.json
            path: config.json
