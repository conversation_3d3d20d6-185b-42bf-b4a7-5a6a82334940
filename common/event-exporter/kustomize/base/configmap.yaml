apiVersion: v1
data:
  config.yaml: |-
    logLevel: warn
    logFormat: json
    metricsNamePrefix: event_exporter_
    route:
      routes:
        - match:
            - receiver: "dump"
            - receiver: "loki"
    receivers:
    - name: "dump"
      stdout: {}
    - name: "loki"
      loki:
        headers: # optional
          'Authorization': Basic ZW5nYWdlc3BhcnJvdzplbmdhZ2VzcGFycm93
        streamLabels:
          container: event-exporter
          clusterName: engagesparrow
        url: https://loki.sparrowdesk.com/loki/api/v1/push
kind: ConfigMap
metadata:
  name: event-exporter-cfg