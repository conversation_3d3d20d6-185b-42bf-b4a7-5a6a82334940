# Kubernetes Event Exporter Deployment

This folder contains the configuration and deployment files for setting up the Kubernetes Event Exporter. The Event Exporter collects Kubernetes events and exports them to a specified logging service such as Loki.

## Prerequisites

Before deploying, ensure you have the following:
- A Loki endpoint (staging or production)
- An Authorization token for Loki

## Configuration

Update the `config.yaml` file with the appropriate values:

### Parameters to Configure

1. **`clusterName`**
   - **Description**: Name of your Kubernetes cluster.
   - **Example**:
     ```yaml
     clusterName: my-cluster
     ```

2. **`url`**
   - **Description**: The URL and authorization of your Loki instance (e.g., production or staging).
   - **Example**:
     ```yaml
    production-us-loki-url: https://us-loki-hmpsbqjete.surveysparrow.com/loki/api/v1/push
    production-us-loki-authorization: Basic bWljcm9zZXJ2aWNlcy1hcDpYUDFQMGpZTlo4SlpLOW0=
    production-eu-loki-url: https://eu-loki-hafpufzovx.surveysparrow.com/loki/api/v1/push
    production-eu-loki-authorization: Basic bWljcm9zZXJ2aWNlcy1ldTo4Zm5mQzlxQzdsT04xTVg=
    production-ap-loki-url: https://ap-loki-vraqhqlcfw.surveysparrow.com/loki/api/v1/push
    production-ap-loki-authorization: Basic bWljcm9zZXJ2aWNlcy1tZTp2YzQ5Q1RYb0QxTGNoN1Q=
    production-me-loki-url: https://me-loki-qeujozrorf.surveysparrow.com/loki/api/v1/push
    production-me-loki-authorization: Basic bWljcm9zZXJ2aWNlcy11czo3R3paSldhMHhodzZlYTY=
    staging-loki-url: https://loki.sparrowdesk.com/loki/api/v1/push
    staging-loki-authorization: Basic ZW5nYWdlc3BhcnJvdzplbmdhZ2VzcGFycm93
     ```

### Example Configuration

Here’s an example `config.yaml`:
```yaml
    logLevel: warn
    logFormat: json
    metricsNamePrefix: event_exporter_
    route:
      routes:
        - match:
            - receiver: "dump"
            - receiver: "loki"
    receivers:
    - name: "dump"
      stdout: {}
    - name: "loki"
      loki:
        headers: # optional
          'Authorization': Basic ZW5nYWdlc3BhcnJvdzplbmdhZ2VzcGFycm93
        streamLabels:
          container: event-exporter
          clusterName: engagesparrow
        url: https://loki.sparrowdesk.com/loki/api/v1/push
```
