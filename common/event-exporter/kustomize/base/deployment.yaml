apiVersion: apps/v1
kind: Deployment
metadata:
  name: event-exporter
spec:
  selector:
    matchLabels:
      app: event-exporter
      version: v1
  template:
    metadata:
      annotations:
        prometheus.io/path: /metrics
        prometheus.io/port: "2112"
        prometheus.io/scrape: "true"
      labels:
        app: event-exporter
        version: v1
    spec:
      containers:
      - args:
        - -conf=/data/config.yaml
        image: ghcr.io/resmoio/kubernetes-event-exporter:v1.7
        imagePullPolicy: IfNotPresent
        name: event-exporter
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - mountPath: /data
          name: cfg
      securityContext:
        runAsNonRoot: true
        seccompProfile:
          type: RuntimeDefault
      serviceAccountName: event-exporter
      volumes:
      - configMap:
          name: event-exporter-cfg
        name: cfg
