apiVersion: apps/v1
kind: Deployment
metadata:
  name: haproxy
spec:
  selector:
    matchLabels:
      app: haproxy
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  template:
    metadata:
      labels:
        app: haproxy
        application: haproxy
    spec:
      securityContext:
        runAsUser: 99
        runAsGroup: 99
      containers:
      - name: haproxy
        image: haproxy:2.6.17
        imagePullPolicy: IfNotPresent
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          runAsNonRoot: true
          readOnlyRootFilesystem: true
          seccompProfile:
            type: RuntimeDefault
        readinessProbe:
          httpGet:
            path: /haproxy/monitor
            port: 8080
          initialDelaySeconds: 20
          timeoutSeconds: 50
          periodSeconds: 60
          failureThreshold: 5
          successThreshold: 1
        livenessProbe:
          httpGet:
            path: /haproxy/monitor
            port: 8080
          initialDelaySeconds: 30
          timeoutSeconds: 50
          periodSeconds: 60
          failureThreshold: 5
          successThreshold: 1
        lifecycle:
          preStop:
            exec:
              command:
              - /bin/sh
              - -c
              - sleep 120
        volumeMounts:
        - name: config
          mountPath: /usr/local/etc/haproxy/haproxy.cfg
          subPath: haproxy.cfg
        ports:
        - containerPort: 80
        - containerPort: 8080
        - containerPort: 1936
        resources:
          requests:
            memory: 100Mi # add node affinity to deploy pod on dedicated node to improve performance
            cpu: 50m
      volumes:
      - name: config
        configMap:
          name: config
          items:
          - key: haproxy.cfg
            path: haproxy.cfg
      terminationGracePeriodSeconds: 150
