apiVersion: batch/v1
kind: CronJob
metadata:
  name: nuclei-scan
spec:
  schedule: "0 6 * * 1"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: nuclei
            image: infosec/nuclei:latest
            resources:
              requests:
                memory: 1800Mi
                cpu: 1000m
              limits:
                memory: 2Gi
            volumeMounts:
            - name: targets-volume
              mountPath: /app/target.txt
              subPath: target.txt
          volumes:
          - name: targets-volume
            configMap:
              name: nuclei-config
              items:
              - key: target.txt
                path: target.txt
          restartPolicy: OnFailure
