apiVersion: apps/v1
kind: Deployment
metadata:
  name: prism-api
  annotations:
    reloader.stakater.com/auto: "true"
spec:
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: prism-api
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 100%
  template:
    spec:
      serviceAccountName: prism-service-account
      terminationGracePeriodSeconds : 30
      containers:
      - name: prism-api
        image: ************.dkr.ecr.us-east-1.amazonaws.com/prism-api:latest
        imagePullPolicy: Always
        readinessProbe:
          httpGet:
            path: /prism-health
            port: 80
            httpHeaders:
            - name: x-probe
              value: READINESS
          initialDelaySeconds: 20
          timeoutSeconds: 15
          periodSeconds: 45
          failureThreshold: 5
          successThreshold: 1
        livenessProbe:
          httpGet:
            path: /prism-health
            port: 80
            httpHeaders:
            - name: x-probe
              value: LIVENESS
          initialDelaySeconds: 20
          timeoutSeconds: 15
          periodSeconds: 45
          failureThreshold: 5
          successThreshold: 1
        ports:
          - containerPort: 80
        resources:
          requests:
            memory: "150Mi"
            cpu: "200m"
          limits:
            memory: "200Mi"
            cpu: "200m"
        env:
          - name: PORT
            value: "80"
        envFrom:
        - secretRef:
            name: prism-secrets
        volumeMounts:
          - name: config-volume
            mountPath: /prism/config.json
            subPath: config.json
      volumes:
        - name: config-volume
          configMap:
            name: prism-config
            items:
            - key: config.json
              path: config.json