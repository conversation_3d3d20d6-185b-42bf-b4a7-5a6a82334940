# Prism

---

Prism is a service used to broadcast API calls to all pods under a Kubernetes deployment. This service can be used for any synchronisation purposes like 
invalidating the stored cache data, extracting or updating current state of the pods by API calls.

In this K8s Manifest Repo. We have kube context files in Kustomize for easy modification and resource generation or management for different environments.

---
**Folder Struture**

---

```
├── common
    └── prism
        └── kustomize
            └── base
                ├── cluster_role.yaml
                ├── cluster_role_binding.yaml
                ├── configmap.yaml
                ├── deployment.yaml
                ├── kustomization.yaml
                ├── namespace.yaml
                ├── service.yaml
                └── service_account.yaml
```

---

## Guidelines (Refer [RFC 2119](https://www.ietf.org/rfc/rfc2119.txt))

---

- You **SHOULD** follow the **Conventional Commits** - [Refer to this document](https://www.conventionalcommits.org/en/v1.0.0/)  
- You **MUST** use only lowercase for file and folder names.  
    - For files: You **MUST NOT** use spaces. Instead, use the underscore symbol (`_`) followed by the file extension.  
    - For folders: You **MUST NOT** use spaces. Use only the hyphen symbol (`-`) instead.  
- In overlays folders, you **SHOULD** name files generically to support other services and environments, minimizing pipeline reusability effort for newer 
  services.  
- Each file name in the overlays folder **MUST** be self-explanatory and easy for developers to understand and modify resources in their environment.  
- You **SHOULD** update the folder structure in the `README.md` file if you have modified any file names or added new files.
- The files inside `base` folder **SHOULD NOT** be fully functional context files. 
- Developers **MUST NOT** change any structure in staging overlay. You can include placeholder values in areas that need to be overridden for staging and production environments.

---

## K8s Contexts File

---

The folder structure **SHOULD** consists of two main components: the **base** folder and the **overlays** folder. The base folder contains all the common context configurations and acts as a template for reusable settings. Files in this folder **SHOULD NOT** be fully functional context files; instead, they should include placeholder values for parameters that need to be overridden in staging and production environments. Each resource type (kind) will have its own dedicated context file within the base folder.

<!--- NOT APPLICABLE TO THIS SERVICE
The overlays folder is divided into two subfolders: `staging` and `production`. ~~The `staging` folder basically allows developers to modify resource values specific to the staging environment. Developers **MUST** only edit files within this folder and avoid modifying the `kustomization.yaml` file unless absolutely necessary. The `production` folder, on the other hand, is used for overrides specific to the production environment and inherits configurations from the base folder. This structure ensures a clear separation between common configurations and environment-specific customizations, promoting better organization and maintainability.  
-->


In the initial phase of the Prism service, JSON files serve as the datastore. They contain essential data for running the Prism service, such as tokens, target selectors, and whitelisted paths. Once the datastore is migrated to a new solution, these JSON files can be deleted.


**`config.json` example**
```json
{
    "route_mappings": {
        "TOKEN": {
            "service": "LABEL",
            "allowed_paths": ["/config-map"],
            "namespace": "NAMESPACE",
            "label_selector": {
                "KEY" :"VALUE"
            },
            "port": "8080",
            "protocol": "http"
        }
    } 
}
```


Currently, we have different node selectors for staging and regions in production. A patch is applied to the node selector in the overlays. Additionally, tolerations are configured only in the staging environment. 

---

> Currently, developers **SHOULD NOT** modify any values in the service. However, they can modify data in the [surveysparrow-staging-config](https://bitbucket.org/surveysparrow/surveysparrow-staging-config) repository.