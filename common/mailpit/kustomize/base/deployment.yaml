apiVersion: apps/v1
kind: Deployment
metadata:
  name: mailpit
  labels:
    application: mailpit
spec:
  selector:
    matchLabels:
      application: mailpit
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: mailpit
        application: mailpit
    spec:
      volumes:
        - name: mailpit-storage
          persistentVolumeClaim:
            claimName: mailpit-storage
      containers:
        - name: mailpit
          image: axllent/mailpit:v1.20
          ports:
            - containerPort: 1025
              protocol: TCP
            - containerPort: 8025
              protocol: TCP
          resources:
            # Local load testing shows 200m CPU and 300Mi memory is sufficient.
            requests:
              cpu: 400m
              memory: 400Mi
            limits:
              memory: 600Mi
          volumeMounts:
            - name: mailpit-storage
              mountPath: /data
          imagePullPolicy: IfNotPresent
          securityContext:
            runAsUser: 0
      terminationGracePeriodSeconds: 60