# Mailpit

---

Mailpit is a service designed to capture and display incoming emails for debugging and testing purposes in a development environment. It acts as a mock mail server to help developers review and inspect emails generated by their application without the need to interact with external email services. Mailpit provides a simple web interface to view email content and can be integrated with the application for real-time email previewing.

In this K8s Manifest Repo, we use Kustomize for easy modification and management of resources across different environments. The manifests for Mailpit are organized into a folder structure that supports both reusable components and environment-specific configurations.

---

**Folder Struture**

---

```
common/mailpit
├── README.md
└── kustomize
    └── base
        ├── deployment.yaml
        ├── kustomization.yaml
        ├── pvc.yaml
        └── service.yaml
```

---

## Guidelines (Refer [RFC 2119](https://www.ietf.org/rfc/rfc2119.txt))

---

- You **SHOULD** follow the **Conventional Commits** - [Refer to this document](https://www.conventionalcommits.org/en/v1.0.0/)  
- You **MUST** use only lowercase for file and folder names.  
    - For files: You **MUST NOT** use spaces. Instead, use the underscore symbol (`_`) followed by the file extension.  
    - For folders: You **MUST NOT** use spaces. Use only the hyphen symbol (`-`) instead.  
- In overlays folders, you **SHOULD** name files generically to support other services and environments, minimizing pipeline reusability effort for newer 
  services.  
- Each file name in the overlays folder **MUST** be self-explanatory and easy for developers to understand and modify resources in their environment.  
- You **SHOULD** update the folder structure in the `README.md` file if you have modified any file names or added new files.
- The files inside `base` folder **SHOULD NOT** be fully functional context filesx. 
- Developers **MUST NOT** change any structure in staging overlay. You can include placeholder values in areas that need to be overridden for staging and production environments.