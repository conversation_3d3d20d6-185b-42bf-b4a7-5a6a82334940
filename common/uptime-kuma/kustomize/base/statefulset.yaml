apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: uptime-kuma
spec:
  selector:
    matchLabels:
      app: uptime-kuma
  serviceName: uptime-kuma-svc
  template:
    metadata:
      labels:
        app: uptime-kuma
    spec:
      containers:
      - name: uptime-kuma
        image: louislam/uptime-kuma:1
        resources:
          limits:
            memory: "500Mi"
          requests:
            memory: "300Mi"
            cpu: "500m"
        ports:
        - containerPort: 3001
        volumeMounts:
        - name: uptime-kuma-pvc
          mountPath: /app/data
      volumes:
      - name: uptime-kuma-pvc
        persistentVolumeClaim:
          claimName: uptime-kuma-pvc
  podManagementPolicy: OrderedReady
  updateStrategy:
    type: RollingUpdate
    rollingUpdate:
      partition: 0
  revisionHistoryLimit: 10
  persistentVolumeClaimRetentionPolicy:
    whenDeleted: Retain
    whenScaled: Retain
