apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: saasspice-website-us-east-1
  namespace: argocd
spec:
  project: default
  source:
    repoURL: *****************:surveysparrow/sparrow-k8s-manifests.git
    targetRevision: master
    path: sparrowgenie/website/kustomize/overlays/production/us-east-1/saasspice
  destination:
    server: https://BE7BCE51CE56942B9C6FDE4B1C7076AF.gr7.us-east-1.eks.amazonaws.com
    namespace: saasspice-website
  ignoreDifferences:
    - group: apps
      jsonPointers:
        - /spec/replicas
      kind: Deployment
      name: website-deployment-saasspice
      namespace: saasspice-website
    - group: apps
      jsonPointers:
        - /spec/replicas
      kind: Deployment
      name: cms-deployment-saasspice
      namespace: saasspice-website
