apiVersion: apps/v1
kind: Deployment
metadata:
  name: deployment
spec:
  selector:
    matchLabels:
      app: saasspice-cms
  template:
    metadata:
      labels:
        app: saasspice-cms
    spec:
      nodeSelector:
        app: saasspice-website
      tolerations:
        - key: "app"
          operator: "Equal"
          value: "saasspice-website"
          effect: "NoSchedule"
      volumes:
      - name: secrets-store-inline
        csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: secrets-saasspice
      serviceAccountName: sa-saasspice
      containers:
      - name: website
        envFrom:
        - configMapRef:
            name: configmap-saasspice
        - secretRef:
            name: saasspice-secrets
