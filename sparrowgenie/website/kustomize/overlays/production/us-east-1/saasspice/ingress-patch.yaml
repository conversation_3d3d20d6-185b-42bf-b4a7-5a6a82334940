apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/group.name: saasspice-website-ingress-group
    alb.ingress.kubernetes.io/load-balancer-name: saasspice-website-alb
    alb.ingress.kubernetes.io/healthcheck-path: /
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:841162696394:certificate/91822d5d-25be-4ca0-ae12-e04f8e7b67c3
spec:
  ingressClassName: my-aws-ingress-class
  defaultBackend:
    service:
      name: website-service-saasspice
      port:
        number: 3000
  rules:
    - http:
        paths:
        - path: /admin
          pathType: Prefix
          backend:
            service:
              name: cms-service-saasspice
              port:
                number: 3000
        - path: /
          pathType: Prefix
          backend:
            service:
              name: website-service-saasspice
              port:
                number: 3000
