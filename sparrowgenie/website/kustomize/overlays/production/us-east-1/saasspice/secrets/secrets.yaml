apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: secrets
spec:
  provider: aws
  parameters:
    region: us-east-1
    objects: |
      - objectName: "saasspice_website"
        objectType: "secretsmanager"
        jmesPath:

          - path: DATABASE_URI
            objectAlias: DATABASE_URI-Alias

          - path: PAYLOAD_SECRET
            objectAlias: PAYLOAD_SECRET-Alias

          - path: CLOUDFLARE_ZONE_ID
            objectAlias: CLOUDFLARE_ZONE_ID-Alias
        
          - path: CLOUDFLARE_API_TOKEN
            objectAlias: CLOUDFLARE_API_TOKEN-Alias
          
          - path: PREVIEW_SECRET
            objectAlias: PREVIEW_SECRET-Alias

          - path: SENTRY_AUTH_TOKEN
            objectAlias: SENTRY_AUTH_TOKEN-Alias
          
  secretObjects:                
    - secretName: saasspice-secrets
      type: Opaque
      data:
        - objectName: "DATABASE_URI-Alias"
          key: DATABASE_URI

        - objectName: "PAYLOAD_SECRET-Alias"
          key: PAYLOAD_SECRET

        - objectName: "CLOUDFLARE_ZONE_ID-Alias"
          key: CLOUDFLARE_ZONE_ID

        - objectName: "CLOUDFLARE_API_TOKEN-Alias"
          key: CLOUDFLARE_API_TOKEN

        - objectName: "PREVIEW_SECRET-Alias"
          key: PREVIEW_SECRET

        - objectName: "SENTRY_AUTH_TOKEN-Alias"
          key: SENTRY_AUTH_TOKEN
