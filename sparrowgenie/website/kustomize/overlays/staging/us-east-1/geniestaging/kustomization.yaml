apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: geniestaging
nameSuffix: -geniestaging
labels:
- pairs:
    layer: geniestaging
resources:
- cms
- website
- secrets
- *****************:surveysparrow/sparrowgenie-staging-config.git//Website/geniestaging?ref=master
- ../../../../../../ingress-base/staging/http-us-east-1/
- arm-karpenter.yaml
- service-account.yaml
- namespace.yaml
images:
- name: website
  newName: ************.dkr.ecr.us-east-1.amazonaws.com/sg-website
  newTag: SGM-96-community-page_2a1d307_179
patches:
- path: ingress-patch.yaml
