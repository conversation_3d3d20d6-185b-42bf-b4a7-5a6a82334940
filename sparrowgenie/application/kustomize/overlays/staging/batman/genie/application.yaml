apiVersion: apps/v1
kind: Deployment
metadata:
  name: application
spec:
  template:
    spec:
      serviceAccountName: batman-application-sa
      volumes:
      - name: secrets-store-inline
        csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: batman-secrets
      containers:
      - name: genie-app
        envFrom:
        - configMapRef:
            name: batman-genie-configmap
        - secretRef:
            name: batman-secrets
        env:
        - name: SPARROW_GENIE_BUNDLE_FINGERPRINT
          value: ca9c8302427bdad32c9da040daa8ec8608153bcf
