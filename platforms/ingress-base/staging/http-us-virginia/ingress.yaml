apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/actions.ssl-redirect: '{"Type": "redirect", "RedirectConfig": { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}'
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/healthcheck-port: traffic-port
    alb.ingress.kubernetes.io/healthcheck-path: /
    alb.ingress.kubernetes.io/healthcheck-protocol: HTTP
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '50'
    alb.ingress.kubernetes.io/healthcheck-timeout-seconds: '30'
    alb.ingress.kubernetes.io/success-codes: '200'
    alb.ingress.kubernetes.io/healthy-threshold-count: '2'
    alb.ingress.kubernetes.io/unhealthy-threshold-count: '5'
    alb.ingress.kubernetes.io/load-balancer-name: ss-staging-microservice-alb
    alb.ingress.kubernetes.io/group.name: ss-staging-microservice-ingress-group
    alb.ingress.kubernetes.io/group.order: '10'
    alb.ingress.kubernetes.io/certificate-arn: >-
      arn:aws:acm:us-east-1:713859105457:certificate/8be40d3d-86f1-4327-be57-d690e2de2ab8,
      arn:aws:acm:us-east-1:713859105457:certificate/808db20b-ddc6-4088-8d25-262a3f110c46,
      arn:aws:acm:us-east-1:713859105457:certificate/643cb37b-ed83-4636-81a8-1f52ea7e56c4
    alb.ingress.kubernetes.io/security-groups: sg-086a204e3eb0f3459
    alb.ingress.kubernetes.io/subnets: subnet-0420050656953dce8, subnet-02e4af8986529e812, subnet-0177bb9a9ec89690c, subnet-0d989b0dab2eb4dee
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/backend-protocol: HTTP
    alb.ingress.kubernetes.io/ssl-policy: ELBSecurityPolicy-TLS13-1-2-2021-06
    alb.ingress.kubernetes.io/tags: CreatedBy=<EMAIL>,Team=Platforms,Service=Platforms
spec:
  ingressClassName: my-aws-ingress-class
  rules: []
