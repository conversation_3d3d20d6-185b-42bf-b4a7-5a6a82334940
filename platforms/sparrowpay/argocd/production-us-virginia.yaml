apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: sparrowpay-platforms-production-us-virginia
  namespace: argocd
spec:
  generators:
    - list:
        elements:
          - name: admin-backend
          - name: gateway
          - name: notifier
          - name: payments
          - name: webhooks
  template:
    metadata:
      name: sparrowpay-{{name}}-platforms-production-us-virginia
    spec:
      project: default
      source:
        repoURL: *****************:surveysparrow/sparrow-k8s-manifests.git
        targetRevision: master
        path: platforms/sparrowpay/kustomize/overlays/production/US-VIRGINIA/{{name}}
      destination:
        server: https://kubernetes.default.svc
        namespace: sparrowpay
