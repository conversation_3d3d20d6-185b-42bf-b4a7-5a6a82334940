apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: sparrowpay-platforms-sandbox-helpsparrow
  namespace: argocd
spec:
  generators:
    - list:
        elements:
          - name: admin-backend
          - name: gateway
          - name: notifier
          - name: payments
          - name: webhooks
  template:
    metadata:
      name: sparrowpay-{{name}}-platforms-sandbox-helpsparrow
    spec:
      project: default
      source:
        repoURL: *****************:surveysparrow/sparrow-k8s-manifests.git
        targetRevision: master
        path: platforms/sparrowpay/kustomize/overlays/sandbox/helpsparrow/{{name}}
      destination:
        server: https://kubernetes.default.svc
        namespace: sparrowpay-helpsparrow
