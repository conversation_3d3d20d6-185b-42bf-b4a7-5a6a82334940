apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: secretprovider
  labels:
    app: webhooks
spec:
  provider: aws
  parameters:
    objects: |
      - objectName: sparrowpay/webhooks/staging
        objectType: secretsmanager
        jmesPath:
          - path: STRIPE_API_KEY
            objectAlias: stripeApiKeyAlias
          - path: STRIPE_WEBHOOK_SECRET
            objectAlias: stripeWebhookSecretAlias
          - path: THRIVE_APIKEY
            objectAlias: thriveApiKeyAlias
          - path: THRIVE_WEBHOOK_SECRET
            objectAlias: thriveWebhookSecretAlias
          - path: SPARROWDESK_APIKEY
            objectAlias: sparrowdeskApiKeyAlias
          - path: SPARROWDESK_WEBHOOK_SECRET
            objectAlias: sparrowdeskWebhookSecretAlias
          - path: LOKI_AUTH
            objectAlias: lokiAuthAlias
          - path: TICKET_API_TOKEN
            objectAlias: ticketApiTokenAlias
  secretObjects:
    - secretName: sparrowpay-webhooks-secrets-envs
      type: Opaque
      data:
        - objectName: stripeApiKeyAlias
          key: STRIPE_API_KEY
        - objectName: stripeWebhookSecretAlias
          key: STRIPE_WEBHOOK_SECRET
        - objectName: thriveApiKeyAlias
          key: THRIVE_APIKEY
        - objectName: thriveWebhookSecretAlias
          key: THRIVE_WEBHOOK_SECRET
        - objectName: sparrowdeskApiKeyAlias
          key: SPARROWDESK_APIKEY
        - objectName: sparrowdeskWebhookSecretAlias
          key: SPARROWDESK_WEBHOOK_SECRET
        - objectName: lokiAuthAlias
          key: LOKI_AUTH
        - objectName: ticketApiTokenAlias
          key: TICKET_API_TOKEN
