apiVersion: apps/v1
kind: Deployment
metadata:
  name: deployment
  labels:
    app: webhooks
    service: sparrowpay
spec:
  selector:
    matchLabels:
      app: webhooks
      service: sparrowpay
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0%
      maxSurge: 100%
  template:
    metadata:
      labels:
        app: webhooks
        service: sparrowpay
    spec:
      volumes:
        - name: secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: us-virginia-sparrowpay-webhooks-secretprovider
      serviceAccountName: service-account
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
      terminationGracePeriodSeconds: 30
      containers:
      - name: webhooks
        image: webhooks:latest
        imagePullPolicy: Always
        lifecycle:
          preStop:
            exec:
              command: 
                - /bin/sh
                -  -c
                - sleep 30
        readinessProbe:
          httpGet:
            path: /health
            port: webhooks
            httpHeaders:
            - name: x-probe
              value: READINESS
          initialDelaySeconds: 10
          timeoutSeconds: 50
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        livenessProbe:
          httpGet:
            path: /health
            port: webhooks
            httpHeaders:
            - name: x-probe
              value: LIVENESS
          initialDelaySeconds: 10
          timeoutSeconds: 50
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        resources:
          requests:
            memory: 512Mi
            cpu: 500m
          limits:
            memory: 1024Mi
        ports:
          - containerPort: 8084
            name: webhooks
        envFrom:
          - configMapRef:
              name: webhooks-config
          - secretRef:
              name: sparrowpay-webhooks-secrets-envs
        volumeMounts:
          - name: secrets-store-inline
            mountPath: /mnt/secrets
            readOnly: true
      nodeSelector:
        app: sparrowpay-microservices
      tolerations:
        - effect: NoSchedule
          operator: Equal
          key: app
          value: platforms-reserved

