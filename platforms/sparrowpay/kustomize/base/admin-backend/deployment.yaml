apiVersion: apps/v1
kind: Deployment
metadata:
  name: deployment
  labels:
    app: admin-backend
    service: sparrowpay
    frontend: admin-backend
spec:
  selector:
    matchLabels:
      app: admin-backend
      service: sparrowpay
      frontend: admin-backend
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0%
      maxSurge: 100%
  template:
    metadata:
      labels:
        app: admin-backend
        service: sparrowpay
        frontend: admin-backend
    spec:
      volumes:
        - name: secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: us-virginia-sparrowpay-admin-backend-secretprovider
      serviceAccountName: service-account
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
      terminationGracePeriodSeconds: 30
      containers:
      - name: admin-backend
        image: admin-backend:latest
        imagePullPolicy: IfNotPresent
        lifecycle:
          preStop:
            exec:
              command: 
                - /bin/sh
                - -c
                - sleep 30
        readinessProbe:
          httpGet:
            path: /api/health
            port: admin-backend
            httpHeaders:
            - name: x-probe
              value: READINESS
          initialDelaySeconds: 8
          timeoutSeconds: 30
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        livenessProbe:
          httpGet:
            path: /api/health
            port: admin-backend
            httpHeaders:
            - name: x-probe
              value: LIVENESS
          initialDelaySeconds: 8
          timeoutSeconds: 30
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        ports:
          - containerPort: 8085
            name: admin-backend
        resources:
          requests:
            memory: 512Mi
            cpu: 500m
          limits:
            memory: 1024Mi
        envFrom:
          - configMapRef:
              name: admin-backend-config
          - configMapRef:
              name: sparrowpay-admin-backend-dist
          - secretRef:
              name: sparrowpay-admin-backend-secrets-envs
        volumeMounts:
          - name: secrets-store-inline
            mountPath: /mnt/secrets
            readOnly: true
      nodeSelector:
        app: sparrowpay-microservices
      tolerations:
        - effect: NoSchedule
          operator: Equal
          key: app
          value: platforms-reserved
