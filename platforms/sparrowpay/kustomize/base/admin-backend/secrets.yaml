apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: secretprovider
  labels:
    app: admin-backend
spec:
  provider: aws
  parameters:
    region: us-east-1
    objects: |
      - objectName: sparrowpay/admin-backend/staging
        objectType: secretsmanager
        jmesPath:
          - path: DB_USERNAME
            objectAlias: dbUsernameAlias
          - path: DB_PASSWORD
            objectAlias: dbPasswordAlias
          - path: SECRET_KEY
            objectAlias: secretKeyAlias
          - path: OAUTH_CLIENTID
            objectAlias: oauthClientIdAlias
          - path: OAUTH_SECRET
            objectAlias: oauthSecretAlias
          - path: COOKIE_KEY
            objectAlias: cookieKeyAlias
          - path: ZIPYKEY
            objectAlias: zipyKeyAlias
          - path: DB_READ_PASSWORD
            objectAlias: dbReadPasswordAlias
          - path: AREA51_POWERBI_PASSWORD
            objectAlias: area51PowerbiPasswordAlias
          - path: LOKI_AUTH
            objectAlias: lokiAuthAlias
  secretObjects:
    - secretName: sparrowpay-admin-backend-secrets-envs
      type: Opaque
      data:
        - objectName: dbUsernameAlias
          key: DB_USERNAME
        - objectName: dbPasswordAlias
          key: DB_PASSWORD
        - objectName: secretKeyAlias
          key: SECRET_KEY
        - objectName: oauthClientIdAlias
          key: OAUTH_CLIENTID
        - objectName: oauthSecretAlias
          key: OAUTH_SECRET
        - objectName: cookieKeyAlias
          key: COOKIE_KEY
        - objectName: zipyKeyAlias
          key: ZIPYKEY
        - objectName: dbReadPasswordAlias
          key: DB_READ_PASSWORD
        - objectName: area51PowerbiPasswordAlias
          key: AREA51_POWERBI_PASSWORD
        - objectName: lokiAuthAlias
          key: LOKI_AUTH
