apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: secretprovider
  labels:
    app: payments
spec:
  provider: aws
  parameters:
    objects: |
      - objectName: sparrowpay/payments/staging
        objectType: secretsmanager
        jmesPath:
          - path: DB_USERNAME
            objectAlias: dbUsernameAlias
          - path: DB_PASSWORD
            objectAlias: dbPasswordAlias
          - path: STRIPE_APIKEY
            objectAlias: stripeApiKeyAlias
          - path: STRIPE_WEBHOOKSECRET
            objectAlias: stripeWebhookSecretAlias
          - path: THRIVE_APIKEY
            objectAlias: thriveApiKeyAlias
          - path: THRIVE_WEBHOOK_SECRET
            objectAlias: thriveWebhookSecretAlias
          - path: SPARROWDESK_APIKEY
            objectAlias: sparrowdeskApiKeyAlias
          - path: SPARROWDESK_WEBHOOK_SECRET
            objectAlias: sparrowdeskWebhookSecretAlias
          - path: DB_READ_PASSWORD
            objectAlias: dbReadPasswordAlias
          - path: TANGO_USERNAME
            objectAlias: tangoUsernameAlias
          - path: TANGO_PASSWORD
            objectAlias: tangoPasswordAlias
          - path: LOKI_AUTH
            objectAlias: lokiAuthAlias
          - path: HUBSPOT_TOKEN
            objectAlias: hubspotTokenAlias
  secretObjects:
    - secretName: sparrowpay-payments-secrets-envs
      type: Opaque
      data:
        - objectName: dbUsernameAlias
          key: DB_USERNAME
        - objectName: dbPasswordAlias
          key: DB_PASSWORD
        - objectName: stripeApiKeyAlias
          key: STRIPE_APIKEY
        - objectName: stripeWebhookSecretAlias
          key: STRIPE_WEBHOOKSECRET
        - objectName: thriveApiKeyAlias
          key: THRIVE_APIKEY
        - objectName: thriveWebhookSecretAlias
          key: THRIVE_WEBHOOK_SECRET
        - objectName: sparrowdeskApiKeyAlias
          key: SPARROWDESK_APIKEY
        - objectName: sparrowdeskWebhookSecretAlias
          key: SPARROWDESK_WEBHOOK_SECRET
        - objectName: dbReadPasswordAlias
          key: DB_READ_PASSWORD
        - objectName: tangoUsernameAlias
          key: TANGO_USERNAME
        - objectName: tangoPasswordAlias
          key: TANGO_PASSWORD
        - objectName: lokiAuthAlias
          key: LOKI_AUTH
        - objectName: hubspotTokenAlias
          key: HUBSPOT_TOKEN
