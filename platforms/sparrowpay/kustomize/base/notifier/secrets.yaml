apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: secretprovider
  labels:
    app: notifier
spec:
  provider: aws
  parameters:
    objects: |
      - objectName: sparrowpay/notifier/staging
        objectType: secretsmanager
        jmesPath:
          - path: DB_USERNAME
            objectAlias: dbUsernameAlias
          - path: DB_PASSWORD
            objectAlias: dbPasswordAlias
          - path: BOT_TOKEN
            objectAlias: botTokenAlias
          - path: DB_READ_PASSWORD
            objectAlias: dbReadPasswordAlias
          - path: LOKI_AUTH
            objectAlias: lokiAuthAlias
          - path: HUBSPOT_BEARER_TOKEN
            objectAlias: hubspotBearerTokenAlias
          - path: HUBSPOT_TOKEN
            objectAlias: hubspotTokenAlias
  secretObjects:
    - secretName: sparrowpay-notifier-secrets-envs
      type: Opaque
      data:
        - objectName: botTokenAlias
          key: BOT_TOKEN
        - objectName: dbUsername<PERSON>lias
          key: DB_USERNAME
        - objectName: dbPassword<PERSON>lias
          key: DB_PASSWORD
        - objectName: dbReadPasswordAlias
          key: DB_READ_PASSWORD
        - objectName: lokiAuthAlias
          key: LOKI_AUTH
        - objectName: hubspotBearerTokenAlias
          key: HUBSPOT_BEARER_TOKEN
        - objectName: hubspotTokenAlias
          key: HUBSPOT_TOKEN
