
apiVersion: apps/v1
kind: Deployment
metadata:
  name: deployment
  labels:
    app: notifier
    service: sparrowpay
spec:
  selector:
    matchLabels:
      app: notifier
      service: sparrowpay
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0%
      maxSurge: 100%
  template:
    metadata:
      labels:
        app: notifier
        service: sparrowpay
    spec:
      volumes:
        - name: secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: us-virginia-sparrowpay-notifier-secretprovider
      serviceAccountName: service-account
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
      terminationGracePeriodSeconds: 30
      containers:
      - name: sparrowpay-notifier
        image: notifier:latest
        imagePullPolicy: IfNotPresent
        lifecycle:
          preStop:
            exec:
              command: 
                - /bin/sh
                -  -c
                - sleep 30
        readinessProbe:
          httpGet:
            path: /health
            port: notifier
            httpHeaders:
            - name: x-probe
              value: READINESS
          initialDelaySeconds: 10
          timeoutSeconds: 50
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        livenessProbe:
          httpGet:
            path: /health
            port: notifier
            httpHeaders:
            - name: x-probe
              value: LIVENESS
          initialDelaySeconds: 10
          timeoutSeconds: 50
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        ports:
          - containerPort: 3000
            name: notifier
        resources:
          requests:
            memory: 256Mi
            cpu: 250m
          limits:
            memory: 512Mi
        envFrom:
          - configMapRef:
              name: notifier-config
          - secretRef:
              name: sparrowpay-notifier-secrets-envs
        volumeMounts:
          - name: secrets-store-inline
            mountPath: /mnt/secrets
            readOnly: true
      nodeSelector:
        app: sparrowpay-spot
      tolerations:
        - effect: NoSchedule
          operator: Equal
          key: app
          value: platforms-reserved

