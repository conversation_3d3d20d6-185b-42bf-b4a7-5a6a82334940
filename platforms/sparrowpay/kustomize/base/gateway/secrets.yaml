apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: secretprovider
  labels:
    app: gateway
spec:
  provider: aws
  parameters:
    objects: |
      - objectName: sparrowpay/gateway/staging
        objectType: secretsmanager
        jmesPath:
          - path: PAYMENTS_CLIENTSECRET
            objectAlias: paymentsClientSecretAlias
          - path: SDK_CLIENTSECRET
            objectAlias: sdkClientSecretAlias
          - path: LOKI_AUTH
            objectAlias: lokiAuthAlias
  secretObjects:
    - secretName: sparrowpay-gateway-secrets-envs
      type: Opaque
      data:
        - objectName: paymentsClientSecretAlias
          key: PAYMENTS_CLIENTSECRET
        - objectName: sdkClientSecretAlias
          key: SDK_CLIENTSECRET
        - objectName: lokiAuthAlias
          key: LOKI_AUTH
