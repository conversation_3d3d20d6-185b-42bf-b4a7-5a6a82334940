apiVersion: apps/v1
kind: Deployment
metadata:
  name: deployment
  labels:
    app: gateway
    service: sparrowpay
spec:
  selector:
    matchLabels:
      app: gateway
      service: sparrowpay
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0%
      maxSurge: 100%
  template:
    metadata:
      labels:
        app: gateway
        service: sparrowpay
    spec: 
      volumes:
        - name: secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: us-virginia-sparrowpay-gateway-secretprovider
      serviceAccountName: service-account
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
      terminationGracePeriodSeconds : 30
      containers:
      - name: sparrowpay-gateway
        image: gateway:latest
        imagePullPolicy: IfNotPresent
        lifecycle:
          preStop:
            exec:
              command: 
                - /bin/sh
                -  -c
                - sleep 30
        readinessProbe:
          httpGet:
            path: /health
            port: gateway
            httpHeaders:
            - name: x-probe
              value: READINESS
          initialDelaySeconds: 5
          timeoutSeconds: 30
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        livenessProbe:
          httpGet:
            path: /health
            port: gateway
            httpHeaders:
            - name: x-probe
              value: LIVENESS
          initialDelaySeconds: 5
          timeoutSeconds: 30
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        ports:
          - containerPort: 8082
            name: gateway
        resources:
          requests:
            memory: 512Mi
            cpu: 500m
          limits:
            memory: 1024Mi
        envFrom:
          - configMapRef:
              name: gateway-config
          - secretRef:
              name: sparrowpay-gateway-secrets-envs
        volumeMounts:
          - name: secrets-store-inline
            mountPath: /mnt/secrets
            readOnly: true
      nodeSelector:
        app: sparrowpay-gateway
      tolerations:
        - effect: NoSchedule
          operator: Equal
          key: app
          value: platforms-reserved