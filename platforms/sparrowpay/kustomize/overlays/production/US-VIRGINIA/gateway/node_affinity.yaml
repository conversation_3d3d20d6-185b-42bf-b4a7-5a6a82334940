apiVersion: apps/v1
kind: Deployment
metadata:
  name: gateway-deployment
spec:
  template:
    spec:
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app
                operator: In
                values:
                - sparrowpay-gateway
            topologyKey: kubernetes.io/hostname
