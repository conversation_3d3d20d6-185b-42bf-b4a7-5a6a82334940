apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:974682937630:certificate/34e0c1da-7a2f-4ea8-aa39-2cb69dfebe7a
    alb.ingress.kubernetes.io/healthcheck-path: /health
    alb.ingress.kubernetes.io/group.name: sparrowpay-ingress-us-group
    alb.ingress.kubernetes.io/load-balancer-name: sparrowpay-us-alb
    alb.ingress.kubernetes.io/target-node-labels: app=sparrowpay-gateway
    alb.ingress.kubernetes.io/tags: CreatedBy=<EMAIL>,Team=SparrowPay,Service=SparrowPay
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=1200,
      deletion_protection.enabled=true,
      access_logs.s3.enabled=true,
      access_logs.s3.bucket=ss-us-alb-logs,
      access_logs.s3.prefix=sparrowpay
spec:
  ingressClassName: my-aws-ingress-class
  rules:
    - host: sparrowpay.surveysparrow.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: gateway-service
                port:
                  number: 8082
