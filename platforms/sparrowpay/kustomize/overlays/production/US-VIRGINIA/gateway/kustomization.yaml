apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: sparrowpay
namePrefix: us-virginia-sparrowpay-

resources:
- ../../../../base/gateway
- ../../../../base/gateway/high-availability
- *****************:surveysparrow/surveysparrow-production-config.git//platforms/sparrowpay/US-VIRGINIA/gateway?ref=master
- provisioner.yaml
- ../../../../../../ingress-base/production/http-us-virginia

patches:
- path: ingress_patch.yaml
- path: secrets_patch.yaml
- path: hpa_patch.yaml
- path: node_affinity.yaml
- path: service_account_patch.yaml

images:
- name: gateway
  newName: ************.dkr.ecr.us-east-1.amazonaws.com/sparrowpay/gateway
  newTag: production-gateway-11-07-2025-1_c679ae631

labels:
- includeSelectors: true
  pairs:
    environment: production
