apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: secretprovider
spec:
  provider: aws
  parameters:
    region: us-east-1
    objects: |
      - objectName: sparrowpay/admin-backend/production
        objectType: secretsmanager
        jmesPath:
          - path: DB_USERNAME
            objectAlias: dbUsernameAlias
          - path: DB_PASSWORD
            objectAlias: dbPasswordAlias
          - path: SECRET_KEY
            objectAlias: secretKeyAlias
          - path: OAUTH_CLIENTID
            objectAlias: oauthClientIdAlias
          - path: OAUTH_SECRET
            objectAlias: oauthSecretAlias
          - path: COOKIE_KEY
            objectAlias: cookieKeyAlias
          - path: ZIPYKEY
            objectAlias: zipyKeyAlias
          - path: DB_READ_PASSWORD
            objectAlias: dbReadPasswordAlias
          - path: AREA51_POWERBI_PASSWORD
            objectAlias: area51PowerbiPasswordAlias
          - path: LOKI_AUTH
            objectAlias: lokiAuthAlias
