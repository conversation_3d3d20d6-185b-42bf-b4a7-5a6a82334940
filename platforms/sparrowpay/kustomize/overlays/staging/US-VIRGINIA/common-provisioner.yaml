apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: microservices-np
spec:
  disruption:
    budgets:
      - nodes: 10%
    consolidateAfter: 0s
    consolidationPolicy: WhenEmptyOrUnderutilized
  limits:
    cpu: 1k
    memory: 1000Gi
  template:
    metadata:
      labels:
        app: sparrowpay-microservices
        intent: app
    spec:
      expireAfter: 720h
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: us-virginia-sparrowpay-microservices-nc
      requirements:
        - key: karpenter.sh/capacity-type
          operator: In
          values:
            - on-demand
        - key: node.kubernetes.io/instance-type
          operator: In
          values:
            - t4g.medium
            - t4g.large
        - key: kubernetes.io/arch
          operator: In
          values:
            - arm64
        - key: topology.kubernetes.io/zone
          operator: In
          values:
            - us-east-1b
        - key: kubernetes.io/os
          operator: In
          values:
            - linux
      taints:
        - effect: NoSchedule
          key: app
          value: platforms-reserved
---
apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: microservices-nc
spec:
  amiFamily: AL2023
  amiSelectorTerms:
  - alias: al2023@latest
  metadataOptions:
    httpPutResponseHopLimit: 3
  subnetSelectorTerms:
  - tags:
      PlatformsK8s: "true"
  securityGroupSelectorTerms:
  - tags:
      PlatformsK8s: "true"
  role: KarpenterNodeRole-Sparrowpay
  tags:
    CreatedBy: <EMAIL>
    IntentLabel: app
    KarpenterProvisionerName: sparrowpay
    Service: SparrowPay
    Team: SparrowPay
    app: sparrowpay
    intent: app
    Name: Karpenter/Sparrowpay
