apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: sparrowpay
namePrefix: us-virginia-sparrowpay-

resources:
- ../../../../base/gateway
- ../../../../base/gateway/high-availability
- *****************:surveysparrow/surveysparrow-staging-config.git//platforms/sparrowpay/US-VIRGINIA/gateway?ref=preproduction-master
- provisioner.yaml
- ../../../../../../ingress-base/staging/http-us-virginia

patches:
- path: ingress_patch.yaml
- path: hpa_patch.yaml
- path: node_affinity.yaml

images:
- name: gateway
  newName: 713859105457.dkr.ecr.us-east-1.amazonaws.com/sparrowpay/gateway
  newTag: master_66af15b96

labels:
- includeSelectors: true
  pairs:
    environment: staging
