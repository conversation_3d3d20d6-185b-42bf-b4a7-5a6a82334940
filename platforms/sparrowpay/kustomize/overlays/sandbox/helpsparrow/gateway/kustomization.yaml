apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: sparrowpay-helpsparrow
namePrefix: helpsparrow-sparrowpay-

resources:
- ../../../../base/gateway
- *****************:surveysparrow/surveysparrow-staging-config.git//platforms/sparrowpay/helpsparrow/gateway?ref=master
- ../../../../../../ingress-base/staging/http-us-virginia

patches:
- path: secrets_patch.yaml
- path: deployment_patch.yaml
- path: ingress_patch.yaml

images:
- name: gateway
  newName: 713859105457.dkr.ecr.us-east-1.amazonaws.com/sparrowpay-helpsparrow/gateway
  newTag: master_5128812220

labels:
- includeSelectors: true
  pairs:
    environment: staging
