apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/healthcheck-path: /health
    alb.ingress.kubernetes.io/target-node-labels: app=sparrowpay-gateway
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=1200,
      deletion_protection.enabled=true,
      access_logs.s3.enabled=true,
      access_logs.s3.bucket=ss-squad-alb-logs,
      access_logs.s3.prefix=ss-microservices-alb
spec:
  ingressClassName: my-aws-ingress-class
  rules:
    - host: sparrowpay.helpsparrow.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: gateway-service
                port:
                  number: 8082
