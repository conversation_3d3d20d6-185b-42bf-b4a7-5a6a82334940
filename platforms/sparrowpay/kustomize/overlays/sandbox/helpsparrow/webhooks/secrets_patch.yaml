apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: secretprovider
spec:
  provider: aws
  parameters:
    objects: |
      - objectName: sparrowpay/webhooks/helpsparrow
        objectType: secretsmanager
        jmesPath:
          - path: STRIPE_API_KEY
            objectAlias: stripeApiKeyAlias
          - path: STRIPE_WEBHOOK_SECRET
            objectAlias: stripeWebhookSecretAlias
          - path: THRIVE_APIKEY
            objectAlias: thriveApiKeyAlias
          - path: THRIVE_WEBHOOK_SECRET
            objectAlias: thriveWebhookSecretAlias
          - path: SPARROWDESK_APIKEY
            objectAlias: sparrowdeskApiKeyAlias
          - path: SPARROWDESK_WEBHOOK_SECRET
            objectAlias: sparrowdeskWebhookSecretAlias
          - path: LOKI_AUTH
            objectAlias: lokiAuthAlias
          - path: TICKET_API_TOKEN
            objectAlias: ticketApiTokenAlias
