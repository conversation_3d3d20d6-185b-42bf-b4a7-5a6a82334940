apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: secretprovider
spec:
  provider: aws
  parameters:
    objects: |
      - objectName: sparrowpay/notifier/helpsparrow
        objectType: secretsmanager
        jmesPath:
          - path: DB_USERNAME
            objectAlias: dbUsernameAlias
          - path: DB_PASSWORD
            objectAlias: dbPasswordAlias
          - path: BOT_TOKEN
            objectAlias: botTokenAlias
          - path: DB_READ_PASSWORD
            objectAlias: dbReadPasswordAlias
          - path: LOKI_AUTH
            objectAlias: lokiAuthAlias
          - path: HUBSPOT_BEARER_TOKEN
            objectAlias: hubspotBearerTokenAlias
          - path: HUBSPOT_TOKEN
            objectAlias: hubspotTokenAlias
