apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: sparrowpay-helpsparrow
namePrefix: helpsparrow-sparrowpay-

resources:
- ../../../../base/notifier
- *****************:surveysparrow/surveysparrow-staging-config.git//platforms/sparrowpay/helpsparrow/notifier?ref=master

patches:
- path: secrets_patch.yaml
- path: deployment_patch.yaml

images:
- name: notifier
  newName: 713859105457.dkr.ecr.us-east-1.amazonaws.com/sparrowpay-helpsparrow/notifier
  newTag: master_14da7c1723

labels:
- includeSelectors: true
  pairs:
    environment: staging
