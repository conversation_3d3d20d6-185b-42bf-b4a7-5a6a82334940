apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: secretprovider
spec:
  provider: aws
  parameters:
    objects: |
      - objectName: sparrowpay/payments/wingssparrow
        objectType: secretsmanager
        jmesPath:
          - path: DB_USERNAME
            objectAlias: dbUsernameAlias
          - path: DB_PASSWORD
            objectAlias: dbPasswordAlias
          - path: STRIPE_APIKEY
            objectAlias: stripeApiKeyAlias
          - path: STRIPE_WEBHOOKSECRET
            objectAlias: stripeWebhookSecretAlias
          - path: THRIVE_APIKEY
            objectAlias: thriveApiKeyAlias
          - path: THRIVE_WEBHOOK_SECRET
            objectAlias: thriveWebhookSecretAlias
          - path: SPARROWDESK_APIKEY
            objectAlias: sparrowdeskApiKeyAlias
          - path: SPARROWDESK_WEBHOOK_SECRET
            objectAlias: sparrowdeskWebhookSecretAlias
          - path: DB_READ_PASSWORD
            objectAlias: dbReadPasswordAlias
          - path: TANGO_USERNAME
            objectAlias: tangoUsernameAlias
          - path: TANGO_PASSWORD
            objectAlias: tangoPasswordAlias
          - path: LOKI_AUTH
            objectAlias: lokiAuthAlias
          - path: HUBSPOT_TOKEN
            objectAlias: hubspotTokenAlias
