apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: secretprovider
spec:
  provider: aws
  parameters:
    objects: |
      - objectName: sparrowpay/gateway/wingssparrow
        objectType: secretsmanager
        jmesPath:
          - path: PAYMENTS_CLIENTSECRET
            objectAlias: paymentsClientSecretAlias
          - path: SDK_CLIENTSECRET
            objectAlias: sdkClientSecretAlias
          - path: LOKI_AUTH
            objectAlias: lokiAuthAlias
