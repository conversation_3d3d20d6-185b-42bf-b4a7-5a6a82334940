apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: sparrowpay-wingssparrow
namePrefix: wingssparrow-sparrowpay-

resources:
- ../../../../base/gateway
- *****************:surveysparrow/surveysparrow-staging-config.git//platforms/sparrowpay/wingssparrow/gateway?ref=master
- ../../../../../../ingress-base/staging/http-us-virginia

patches:
- path: secrets_patch.yaml
- path: deployment_patch.yaml
- path: ingress_patch.yaml

images:
- name: gateway
  newName: 713859105457.dkr.ecr.us-east-1.amazonaws.com/sparrowpay-wingssparrow/gateway
  newTag: master_695652594

labels:
- includeSelectors: true
  pairs:
    environment: staging
