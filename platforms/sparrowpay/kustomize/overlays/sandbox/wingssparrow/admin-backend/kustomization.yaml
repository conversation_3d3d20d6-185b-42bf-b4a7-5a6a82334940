apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: sparrowpay-wingssparrow
namePrefix: wingssparrow-sparrowpay-

resources:
- ../../../../base/admin-backend
- *****************:surveysparrow/surveysparrow-staging-config.git//platforms/sparrowpay/wingssparrow/admin-backend?ref=master

patches:
- path: secrets_patch.yaml
- path: deployment_patch.yaml

images:
- name: admin-backend
  newName: 713859105457.dkr.ecr.us-east-1.amazonaws.com/sparrowpay-wingssparrow/admin-backend
  newTag: master_695652598

labels:
- includeSelectors: true
  pairs:
    environment: staging
