apiVersion: batch/v1
kind: Job
metadata:
  generateName: ${NAME_PREFIX}-heimdall-application-migration-
  namespace: ${NAMESPACE}
  labels:
    migration/id: "${MIGRATION_ID}"
    migration/build: "${BUILD_NUMBER}"
    migration/type: ${MIGRATION_TYPE}
spec:
  template:
    metadata:
      labels:
        migration/id: "${MIGRATION_ID}"
        migration/build: "${BUILD_NUMBER}"
        migration/type: ${MIGRATION_TYPE}
      annotations:
        eks.amazonaws.com/role-arn: arn:aws:iam::${ACCOUNT_ID}:role/${IAM_ROLE_NAME}
    spec:
      serviceAccountName: ${NAME_PREFIX}-heimdall-service-account
      containers:
      - name: heimdall
        image: ${ACCOUNT_ID}.dkr.ecr.${REGION}.amazonaws.com/${BASE_IMAGE_NAME}/application:latest
        imagePullPolicy: Always
        lifecycle:
          preStop:
            exec:
              command:
              - /bin/sh
              - -c
              - sleep 30
        resources:
          requests:
            cpu: 600m
            memory: 256Mi
          limits:
            memory: 800Mi
        envFrom:
        - configMapRef:
            name: ${NAME_PREFIX}-heimdall-application-migration-config
        env:
        - name: profile
          value: ${MIGRATION_TYPE}
      nodeSelector:
        app: heimdall
      tolerations:
      - key: app
        operator: Equal
        value: platforms-reserved
        effect: NoSchedule
      restartPolicy: Never
      terminationGracePeriodSeconds: 30
  backoffLimit: 0
