apiVersion: karpenter.sh/v1beta1
kind: NodePool
metadata:
  name: np
spec:
  disruption:
    consolidationPolicy: WhenUnderutilized
  limits:
    cpu: "1000"
    memory: 1000Gi
  template:
    metadata:
      labels:
        app: heimdall
        intent: app
    spec:
      nodeClassRef:
        name: us-virginia-heimdall-nc
      taints:
        - key: app
          value: platforms-reserved
          effect: NoSchedule
      requirements:
      - key: karpenter.sh/capacity-type
        operator: In
        values:
        - on-demand
      - key: node.kubernetes.io/instance-type
        operator: In
        values:
        - t3a.medium
        - t3a.large
      - key: topology.kubernetes.io/zone
        operator: In
        values:
        - us-east-1a
        - us-east-1b
        - us-east-1c
---
apiVersion: karpenter.k8s.aws/v1beta1
kind: EC2NodeClass
metadata:
  name: nc
spec:
  amiFamily: AL2
  role: KarpenterNodeRole-Heimdall
  securityGroupSelectorTerms:
  - tags:
      PlatformsK8s: "true"
  subnetSelectorTerms:
  - tags:
      PlatformsK8s: "true"
  tags:
    CreatedBy: <EMAIL>
    IntentLabel: app
    KarpenterProvisionerName: heimdall
    Service: Heimdall
    Team: ReputationManagement
    app: heimdall
    intent: app
    Name: Karpenter/heimdall
  metadataOptions:
    httpPutResponseHopLimit: 3
    httpTokens: required
