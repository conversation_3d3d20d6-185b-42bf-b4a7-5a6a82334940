apiVersion: apps/v1
kind: Deployment
metadata:
  name: application
spec:
  template:
    metadata:
      labels:
        application: heimdall-us
      annotations:
        eks.amazonaws.com/role-arn: arn:aws:iam::974682937630:role/HeimdallServiceRole
    spec:
      containers:
        - name: heimdall
          resources:
            requests:
              cpu: 1000m
              memory: 1Gi
            limits:
              cpu: 2000m
              memory: 2Gi
      tolerations:
      - key: "app"
        operator: "Equal"
        value: "platforms-reserved"
        effect: "NoSchedule"