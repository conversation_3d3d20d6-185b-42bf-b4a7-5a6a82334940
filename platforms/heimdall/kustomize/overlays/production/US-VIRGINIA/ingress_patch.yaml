apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/group.name: heimdall-us-group
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=1200,deletion_protection.enabled=true,access_logs.s3.enabled=true,access_logs.s3.bucket=ss-us-alb-logs,access_logs.s3.prefix=heimdall
    alb.ingress.kubernetes.io/load-balancer-name: heimdall-us-alb
    alb.ingress.kubernetes.io/tags: CreatedBy=<EMAIL>,Team=ReputationManagement,Service=Heimdall
    alb.ingress.kubernetes.io/target-node-labels: app=heimdall
spec:
  rules:
    - host: heimdall-us.platforms.surveysparrow.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ssl-redirect
                port: 
                  name: use-annotation
          - path: /
            pathType: Prefix
            backend:
              service:
                name: application-service
                port:
                  number: 8081
