apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: np
  labels:
    app: heimdall
    intent: app
spec:
  template:
    metadata:
      labels:
        app: heimdall
        intent: app
    spec:
      requirements:
      - key: karpenter.sh/capacity-type
        operator: In
        values: 
        - on-demand
      - key: node.kubernetes.io/instance-type
        operator: In
        values:
        - t3a.small
        - t3a.medium
        - t3a.large
      - key: topology.kubernetes.io/zone
        operator: In
        values: 
        - ap-south-1a
        - ap-south-1b
        - ap-south-1c
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: ap-mumbai-heimdall-nc
      taints:
      - key: app
        value: platforms-reserved
        effect: NoSchedule
      expireAfter: Never
  limits:
    cpu: 1000
    memory: 1000Gi
  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
    consolidateAfter: 1m
    budgets:
    - nodes: "1"
---
apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: nc
  labels:
    app: heimdall
    intent: app
spec:
  amiFamily: AL2023
  amiSelectorTerms:
  - alias: al2023@latest
  role: KarpenterNodeRole-Heimdall
  subnetSelectorTerms:
  - tags:
      PlatformsK8s: "true"
  securityGroupSelectorTerms:
  - tags:
      PlatformsK8s: "true"
  tags:
    Service: Heimdall
    CreatedBy: <EMAIL>
    Team: ReputationManagement
    app: heimdall
    KarpenterProvisionerName: heimdall
    environment: production
    IntentLabel: app
    intent: app
    Name: Karpenter/heimdall
  metadataOptions:
    httpPutResponseHopLimit: 3
    httpTokens: required