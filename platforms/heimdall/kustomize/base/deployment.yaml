apiVersion: apps/v1
kind: Deployment
metadata:
  name: application
  labels:
    app: heimdall
    service: application
spec:
  revisionHistoryLimit: 5
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: heimdall
      service: application
  template:
    metadata:
      labels:
        app: heimdall
        service: application
      annotations:
        eks.amazonaws.com/role-arn: arn:aws:iam::************:role/HeimdallServiceRole
    spec:
      serviceAccountName: service-account
      containers:
        - name: heimdall
          image: application:latest
          imagePullPolicy: IfNotPresent
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/sh
                  - -c
                  - sleep 30
          resources:
            requests:
              cpu: 500m
              memory: 512Mi
            limits:
              cpu: 1000m
              memory: 1000Mi
          ports:
            - containerPort: 8081
              name: http-server
          readinessProbe:
            httpGet:
              path: /inHouse/healthCheck
              port: 8081
            initialDelaySeconds: 120
            timeoutSeconds: 15
            periodSeconds: 60
            failureThreshold: 5
            successThreshold: 1
          livenessProbe:
            httpGet:
              path: /inHouse/healthCheck
              port: 8081
            initialDelaySeconds: 120
            timeoutSeconds: 15
            periodSeconds: 60
            failureThreshold: 5
            successThreshold: 1
          envFrom:
          - configMapRef:
              name: config
          - secretRef:
              name: secrets
      nodeSelector:
        app: heimdall
      tolerations:
      - key: app
        operator: Equal
        value: platforms-reserved
        effect: NoSchedule
