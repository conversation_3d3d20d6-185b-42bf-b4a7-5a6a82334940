apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:974682937630:certificate/34e0c1da-7a2f-4ea8-aa39-2cb69dfebe7a, arn:aws:acm:us-east-1:974682937630:certificate/4a68a520-da0b-4299-90e2-5a037c23c4a5
    alb.ingress.kubernetes.io/healthcheck-path: /healthz
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=1200,deletion_protection.enabled=true,access_logs.s3.enabled=true,access_logs.s3.bucket=ss-us-alb-logs,access_logs.s3.prefix=monitoring-us-alb
    alb.ingress.kubernetes.io/load-balancer-name: monitoring-us-alb
    alb.ingress.kubernetes.io/group.name: monitoring-us-group
    alb.ingress.kubernetes.io/security-groups: sg-0bce84ceec62ad349
spec:
  rules:
    - host: platform-grafana.surveysparrow.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: prometheus-grafana
                port:
                  number: 80
