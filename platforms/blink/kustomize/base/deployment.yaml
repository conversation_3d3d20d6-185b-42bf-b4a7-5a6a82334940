apiVersion: apps/v1
kind: Deployment
metadata:
  name: application
  labels:
    app: blink
    service: application
spec:
  revisionHistoryLimit: 5
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: blink
      service: application
  template:
    metadata:
      labels:
        app: blink
        service: application
      annotations:
        eks.amazonaws.com/role-arn: arn:aws:iam::************:role/BlinkServiceRole
    spec:
      serviceAccountName: service-account
      containers:
        - name: blink
          image: application:latest
          imagePullPolicy: IfNotPresent
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/sh
                  - -c
                  - sleep 30
          resources:
            requests:
              cpu: 500m
              memory: 512Mi
            limits:
              cpu: 1000m
              memory: 1000Mi
          env:
          - name: BLINK_ENV
            value: staging
          ports:
            - containerPort: 8080
              name: http-server
          readinessProbe:
            httpGet:
              path: /health
              port: http-server
            initialDelaySeconds: 15
            periodSeconds: 5
            successThreshold: 1
            failureThreshold: 4
          livenessProbe:
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 15
            periodSeconds: 60
            successThreshold: 1
            failureThreshold: 4
          volumeMounts:
            - name: config-json
              mountPath: /etc/blink/
      volumes:
        - name: config-json
          configMap:
            name: config
            defaultMode: 420
      nodeSelector:
        app: blink
      tolerations:
      - key: app
        operator: Equal
        value: platforms-reserved
        effect: NoSchedule
      terminationGracePeriodSeconds : 30
            