apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namePrefix: us-virginia-blink-
namespace: blink

resources:
- ../../../base
- ../../../base/high-availability
- *****************:surveysparrow/surveysparrow-staging-config.git/platforms/blink/US-VIRGINIA?ref=preproduction-master
- ../../../../../ingress-base/staging/http-us-virginia
- provisioner.yaml

images:
- name: application
  newName: 713859105457.dkr.ecr.us-east-1.amazonaws.com/blink/application
  newTag: preproduction-application-blink-05may2025-4_d5115fd11


patches:
- path: ingress_patch.yaml
- path: hpa_replica_patch.yaml
