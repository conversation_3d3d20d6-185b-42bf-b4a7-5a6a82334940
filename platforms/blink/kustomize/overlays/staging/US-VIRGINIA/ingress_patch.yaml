apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/healthcheck-path: /check/health
    alb.ingress.kubernetes.io/target-node-labels: app=blink
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=1200,deletion_protection.enabled=true,access_logs.s3.enabled=true,access_logs.s3.bucket=ss-squad-alb-logs,access_logs.s3.prefix=ss-microservices-alb
spec:
  rules:
    - host: blink.salesparrow.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: application-service
                port:
                  number: 8080
    - host: sms.s5.engagesparrow.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: application-service
                port:
                  number: 8080
    - host: sms.s2.engagesparrow.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: application-service
                port:
                  number: 8080
    - host: stage.tsprw.io
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: application-service
                port:
                  number: 8080
