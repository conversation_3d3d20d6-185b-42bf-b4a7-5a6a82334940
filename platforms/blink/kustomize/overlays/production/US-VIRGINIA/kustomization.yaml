apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namePrefix: us-virginia-blink-
namespace: blink

resources:
- ../../../base
- ../../../base/high-availability
- ../../../../../ingress-base/production/http-us-virginia
- *****************:surveysparrow/surveysparrow-production-config.git/platforms/blink/US-VIRGINIA?ref=master
- provisioner.yaml

images:
- name: application
  newName: ************.dkr.ecr.us-east-1.amazonaws.com/blink/application
  newTag: production-application-08-05-2025-2_d5115fd4

patches:
- path: ingress_patch.yaml
- path: hpa_replica_patch.yaml
- path: service_account_patch.yaml
- path: deployment_patch.yaml
