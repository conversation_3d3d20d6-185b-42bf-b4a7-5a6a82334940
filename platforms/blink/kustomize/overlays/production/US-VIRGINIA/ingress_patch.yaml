apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/group.name: blink-us-group
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=1200,deletion_protection.enabled=true,access_logs.s3.enabled=true,access_logs.s3.bucket=ss-us-alb-logs,access_logs.s3.prefix=blink
    alb.ingress.kubernetes.io/load-balancer-name: blink-us-alb
    alb.ingress.kubernetes.io/tags: CreatedBy=<EMAIL>,Team=SRE,Service=blink
    alb.ingress.kubernetes.io/target-node-labels: app=blink
spec:
  rules:
    - http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ssl-redirect
                port: 
                  name: use-annotation
    - host: sprw.io
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: application-service
                port:
                  number: 8080
    - host: tsprw.io
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: application-service
                port:
                  number: 8080
    - host: blink.surveysparrow.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: application-service
                port:
                  number: 8080
