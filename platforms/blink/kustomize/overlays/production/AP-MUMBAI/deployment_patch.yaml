apiVersion: apps/v1
kind: Deployment
metadata:
  name: application
spec:
  template:
    metadata:
      annotations:
        eks.amazonaws.com/role-arn: arn:aws:iam::974682937630:role/BlinkServiceRole
    spec:
      containers:
        - name: blink
          resources:
            requests:
              cpu: 1000m
              memory: 2Gi
            limits:
              memory: 2Gi
          env:
            - name: NEWRELIC_LICENSEKEY
              value: ffb82aa01c7340f402eb0ca07f2b8babFFFFNRAL
            - name: NEWRELIC_APPNAME
              value: Blink-AP
            - name: BLINK_ENV
              value: production