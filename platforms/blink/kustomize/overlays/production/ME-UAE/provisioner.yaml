apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: np
  labels:
    app: blink
    intent: app
spec:
  template:
    metadata:
      labels:
        app: blink
        intent: app
    spec:
      requirements:
      - key: kubernetes.io/arch
        operator: In
        values: 
        - arm64
      - key: kubernetes.io/os
        operator: In
        values: 
        - linux
      - key: karpenter.sh/capacity-type
        operator: In
        values: 
        - on-demand
      - key: node.kubernetes.io/instance-type
        operator: In
        values:
        - t4g.small
        - t4g.medium
        - t4g.large
      - key: topology.kubernetes.io/zone
        operator: In
        values: 
        - me-central-1a
        - me-central-1b 
        - me-central-1c
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: me-uae-blink-nc
      taints:
      - key: app
        value: platforms-reserved
        effect: NoSchedule
      expireAfter: Never
  limits:
    cpu: 1000
    memory: 1000Gi
  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
    consolidateAfter: 1m
---
apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: nc
  labels:
    app: blink
    intent: app
spec:
  amiFamily: AL2023
  amiSelectorTerms:
  - alias: al2023@latest
  role: KarpenterNodeRole-Blink
  subnetSelectorTerms:
  - tags:
      PlatformsK8s: "true"
  securityGroupSelectorTerms:
  - tags:
      PlatformsK8s: "true"
  tags:
    Service: Blink
    CreatedBy: <EMAIL>
    Team: SRE
    app: blink
    KarpenterProvisionerName: blink
    environment: production
    IntentLabel: app
    intent: app
    Name: Karpenter/blink