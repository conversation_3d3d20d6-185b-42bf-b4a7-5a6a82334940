apiVersion: batch/v1
kind: Job
metadata:
  generateName: ${NAME_PREFIX}-attainment-application-migration-
  namespace: ${NAMESPACE}
  labels:
    migration/id: "${MIGRATION_ID}"
    migration/build: "${BUILD_NUMBER}"
    migration/type: ${MIGRATION_TYPE}
spec:
  template:
    metadata:
      labels:
        migration/id: "${MIGRATION_ID}"
        migration/build: "${BUILD_NUMBER}"
        migration/type: ${MIGRATION_TYPE}
    spec:
      containers:
      - name: attainment-migration
        image: ${ACCOUNT_ID}.dkr.ecr.${REGION}.amazonaws.com/attainment/application:latest
        imagePullPolicy: Always
        command: ["sh", "-c"]
        args:
        - sleep 5 && node dist/src/migrations/index.js
        lifecycle:
          preStop:
            exec:
              command:
              - /bin/sh
              - -c
              - sleep 30
        resources:
          requests:
            cpu: 500m
            memory: 512Mi
          limits:
            memory: 512Mi
            cpu: 600m
        envFrom:
        - configMapRef:
            name: ${NAME_PREFIX}-attainment-application-migration-config
        - secretRef:
            name: attainment-application-secrets-envs
        env:
        - name: MIGRATION_TYPE
          value: ${MIGRATION_TYPE}
        - name: NODE_ENV
          value: ${environment}
      restartPolicy: Never
      terminationGracePeriodSeconds: 30
      nodeSelector:
        app: attainment
      tolerations:
      - key: app
        operator: Equal
        value: platforms-reserved
        effect: NoSchedule
  ttlSecondsAfterFinished: 300  # Cleanup after 5 minutes
  backoffLimit: 0