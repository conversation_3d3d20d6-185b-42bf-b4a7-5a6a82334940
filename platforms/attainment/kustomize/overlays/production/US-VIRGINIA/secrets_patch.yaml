apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: secretprovider
spec:
  provider: aws
  parameters: 
    objects: |
      - objectName: attainment/application/production
        objectType: secretsmanager
        jmesPath:
          - path: DB_PASSWORD
            objectAlias: dbPasswordAlias
          - path: DB_READ_PASSWORD
            objectAlias: dbReadPasswordAlias
          - path: SAML_CERTIFICATE
            objectAlias: samlCertificateAlias
          - path: JWT_SECRET
            objectAlias: jwtSecretAlias
          - path: HUBSPOT_TOKEN
            objectAlias: hubspotTokenAlias
