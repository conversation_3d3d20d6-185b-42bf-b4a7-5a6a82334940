apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: np
spec:
  template:
    metadata:
      labels:
        app: attainment
      annotations:
        Name: attainment
        NodePool: attainment
        CreatedBy: <EMAIL>
    spec:
      expireAfter: 5h
      nodeClassRef:
        name: us-virginia-attainment-nc
        group: karpenter.k8s.aws
        kind: EC2NodeClass
      taints:
      - key: app
        value: platforms-reserved
        effect: NoSchedule
      requirements:
      - key: karpenter.sh/capacity-type
        operator: In
        values:
        - spot
        - on-demand
      - key: node.kubernetes.io/instance-type
        operator: In
        values:
        - t4g.small
      - key: topology.kubernetes.io/zone
        operator: In
        values:
        - us-east-1b
      - key: kubernetes.io/arch
        operator: In
        values:
        - arm64
      - key: kubernetes.io/os
        operator: In
        values:
        - linux
  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
    consolidateAfter: 0s
  limits:
    cpu: 100
    memory: 800Gi
---
apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: nc
spec:
  amiFamily: AL2023
  amiSelectorTerms:
  - alias: al2023@latest
  metadataOptions:
    httpPutResponseHopLimit: 3
  subnetSelectorTerms:
  - tags:
      PlatformsK8s: "true"
  securityGroupSelectorTerms:
  - tags:
      PlatformsK8s: "true"
  role: KarpenterNodeRole-Attainment
  tags:
    KarpenterProvisionerName: Attainment
    CreatedBy: <EMAIL>
    Team: Billing
    Environment: production
    Application: Attainment
    CostCenter: CC-Attainment
    ManagedBy: Karpenter
    Service: Attainment
    IntentLabel: app
    app: attainment
    intent: app
    Name: Karpenter/attainment
