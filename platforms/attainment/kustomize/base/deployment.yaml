apiVersion: apps/v1
kind: Deployment
metadata:
  name: application
  labels:
    app: attainment
    service: attainment
    frontend: frontend
spec:
  selector:
    matchLabels:
      app: attainment
      service: attainment
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0%
      maxSurge: 100%
  template:
    metadata:
      labels:
        app: attainment
        service: attainment
        frontend: frontend
    spec:
      volumes:
        - name: secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: us-virginia-attainment-secretprovider
      serviceAccountName: service-account
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
      terminationGracePeriodSeconds: 30
      containers:
      - name: application
        image: application:latest
        imagePullPolicy: IfNotPresent
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          runAsNonRoot: true
          readOnlyRootFilesystem: true
          seccompProfile:
            type: RuntimeDefault
        lifecycle:
          preStop:
            exec:
              command: 
                - /bin/sh
                -  -c
                - sleep 30
        readinessProbe:
          httpGet:
            path: /api/health
            port: attainment
            httpHeaders:
            - name: x-probe
              value: READINESS
          initialDelaySeconds: 8
          timeoutSeconds: 20
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        livenessProbe:
          httpGet:
            path: /api/health
            port: attainment
            httpHeaders:
            - name: x-probe
              value: LIVENESS
          initialDelaySeconds: 8
          timeoutSeconds: 20
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        resources:
          requests:
            memory: 512Mi
            cpu: 500m
          limits:
            memory: 800Mi
        ports:
          - containerPort: 8086
            name: attainment
        envFrom:
          - configMapRef:
              name: config
          - configMapRef:
              name: attainment-frontend-dist
          - secretRef:
              name: attainment-application-secrets-envs
        volumeMounts:
          - name: secrets-store-inline
            mountPath: /mnt/secrets
            readOnly: true
      nodeSelector:
        app: attainment
      tolerations:
        - effect: NoSchedule
          operator: Equal
          key: app
          value: platforms-reserved

