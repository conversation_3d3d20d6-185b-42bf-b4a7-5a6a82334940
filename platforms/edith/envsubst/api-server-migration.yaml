apiVersion: batch/v1
kind: Job
metadata:
  generateName: ${NAME_PREFIX}-edith-api-server-migration-
  namespace: ${NAMESPACE}
  labels:
    migration/id: "${MIGRATION_ID}"
    migration/build: "${BUILD_NUMBER}"
    migration/type: ${MIGRATION_TYPE}
spec:
  template:
    metadata:
      labels:
        migration/id: "${MIGRATION_ID}"
        migration/build: "${BUILD_NUMBER}"
        migration/type: ${MIGRATION_TYPE}
      annotations:
        eks.amazonaws.com/role-arn: arn:aws:iam::${ACCOUNT_ID}:role/${IAM_ROLE_NAME}
    spec:
      serviceAccountName: ${NAME_PREFIX}-edith-service-account
      containers:
      - name: edith
        image: ${ACCOUNT_ID}.dkr.ecr.${REGION}.amazonaws.com/${BASE_IMAGE_NAME}/api-server:latest
        imagePullPolicy: Always
        command: ["sh", "-c"]
        args:
          - sleep 10 && ./bin/migrator
        lifecycle:
          preStop:
            exec:
              command:
              - /bin/sh
              - -c
              - sleep 30
        resources:
          requests:
            cpu: 600m
            memory: 256Mi
          limits:
            memory: 800Mi
        env:
        - name: EDITH_CONFIG_DIR
          value: /app
        - name: EDITH_ENV
          value: ${ENVIRONMENT}
        - name: AWS_S3_ENDPOINT
          value: s3.dualstack.us-east-1.amazonaws.com
        - name: MIGRATION_TYPE
          value: ${MIGRATION_TYPE}
        volumeMounts:
        - name: config-json
          mountPath: /app/config/
      volumes:
      - name: config-json
        configMap:
          name: ${NAME_PREFIX}-edith-api-server-migration-config
          defaultMode: 420
      nodeSelector:
        app: edith
      tolerations:
      - key: app
        operator: Equal
        value: platforms-reserved
        effect: NoSchedule
      restartPolicy: Never
      terminationGracePeriodSeconds: 30
  ttlSecondsAfterFinished: 3600  # Cleanup after 1 hour
  backoffLimit: 0
