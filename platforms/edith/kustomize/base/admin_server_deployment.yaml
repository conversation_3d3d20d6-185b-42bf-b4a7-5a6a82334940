apiVersion: apps/v1
kind: Deployment
metadata:
  name: admin-server
  labels:
    app: edith-admin-server
    service: edith
spec:
  revisionHistoryLimit: 5
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: edith-admin-server
  template:
    metadata:
      labels:
        app: edith-admin-server
      annotations:
        eks.amazonaws.com/role-arn: arn:aws:iam::************:role/EdithServiceRole
    spec:
      serviceAccountName: service-account
      containers:
      - name: edith
        image: admin-server:latest
        imagePullPolicy: Always
        lifecycle:
          preStop:
            exec:
              command:
              - /bin/sh
              - -c
              - sleep 15
        resources:
          requests:
            cpu: 300m
            memory: 512Mi
          limits:
            memory: 512Mi
        env:
        - name: EDITH_CONFIG_DIR
          value: /app
        - name: EDITH_ENV
          value: staging
        - name: AWS_S3_ENDPOINT
          value: s3.dualstack.us-east-1.amazonaws.com
        ports:
        - containerPort: 8080
          name: http-server
        readinessProbe:
          httpGet:
            path: /check/health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 4
        livenessProbe:
          httpGet:
            path: /check/health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 4
        volumeMounts:
        - name: config-json
          mountPath: /app/config/
      volumes:
      - name: config-json
        configMap:
          name: config
          defaultMode: 420
      nodeSelector:
        app: edith
      tolerations:
      - key: app
        operator: Equal
        value: platforms-reserved
        effect: NoSchedule
      terminationGracePeriodSeconds: 15
