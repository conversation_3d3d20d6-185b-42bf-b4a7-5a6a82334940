apiVersion: apps/v1
kind: Deployment
metadata:
  name: worker
  labels:
    app: edith-worker
    service: edith
spec:
  revisionHistoryLimit: 5
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app: edith-worker
  template:
    metadata:
      labels:
        app: edith-worker
      annotations:
        eks.amazonaws.com/role-arn: arn:aws:iam::************:role/EdithServiceRole
    spec:
      serviceAccountName: service-account
      containers:
      - name: edith
        image: worker:latest
        imagePullPolicy: Always
        lifecycle:
          preStop:
            exec:
              command:
              - /bin/sh
              - -c
              - sleep 30
        resources:
          requests:
            cpu: 500m
            memory: 256Mi
          limits:
            memory: 512Mi
        env:
        - name: EDITH_CONFIG_DIR
          value: /app
        - name: EDITH_ENV
          value: staging
        - name: AWS_S3_ENDPOINT
          value: s3.dualstack.us-east-1.amazonaws.com
        volumeMounts:
        - name: config-json
          mountPath: /app/config/
        ports:
        - containerPort: 8080
          name: http-server
        readinessProbe:
          httpGet:
            path: /check/health-worker
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 5
          timeoutSeconds: 20
          successThreshold: 1
          failureThreshold: 4
        livenessProbe:
          httpGet:
            path: /check/health-worker
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 60
          timeoutSeconds: 20
          successThreshold: 1
          failureThreshold: 4
      volumes:
      - name: config-json
        configMap:
          name: config
          defaultMode: 420
      nodeSelector:
        app: edith
      tolerations:
      - key: app
        operator: Equal
        value: platforms-reserved
        effect: NoSchedule
      terminationGracePeriodSeconds: 30

