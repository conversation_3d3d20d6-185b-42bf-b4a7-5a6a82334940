apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: np
spec:
  template:
    metadata:
      labels:
        app: edith-typesparrow
      annotations:
        Name: edith
        NodePool: edith
        CreatedBy: <EMAIL>
    spec:
      expireAfter: 720h
      nodeClassRef:
        name: typesparrow-edith-nc
        group: karpenter.k8s.aws
        kind: EC2NodeClass
      taints:
      - key: app
        value: platforms-reserved
        effect: NoSchedule
      requirements:
      - key: karpenter.sh/capacity-type
        operator: In
        values:
        - on-demand
      - key: node.kubernetes.io/instance-type
        operator: In
        values:
        - t4g.small
        - t4g.medium
      - key: topology.kubernetes.io/zone
        operator: In
        values:
        - us-east-1b
      - key: kubernetes.io/arch
        operator: In
        values:
        - arm64
      - key: kubernetes.io/os
        operator: In
        values:
        - linux
  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
  limits:
    cpu: 30
    memory: 60Gi
---
apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: nc
spec:
  amiFamily: AL2023
  amiSelectorTerms:
  - alias: al2023@latest
  metadataOptions:
    httpPutResponseHopLimit: 3
  subnetSelectorTerms:
  - tags:
      PlatformsK8s: "true"
  securityGroupSelectorTerms:
  - tags:
      PlatformsK8s: "true"
  role: KarpenterNodeRole-Edith
  tags:
    environment: staging
    KarpenterProvisionerName: edith
    CreatedBy: <EMAIL>
    Team: Hygiene
    Service: Edith
    IntentLabel: app
    app: edith-typesparrow
    intent: app
    Name: Karpenter/edith-typesparrow
