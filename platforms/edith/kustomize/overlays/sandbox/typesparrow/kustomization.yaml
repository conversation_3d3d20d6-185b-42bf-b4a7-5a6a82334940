apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../../base
- ../../../../../ingress-base/staging/http-us-virginia
- ../../../../../../common/mailpit/kustomize/base
- provisioner.yaml
- *****************:surveysparrow/surveysparrow-staging-config.git/platforms/edith/typesparrow?ref=master

namePrefix: typesparrow-edith-

namespace: edith-typesparrow

images:
- name: admin-server
  newName: ************.dkr.ecr.us-east-1.amazonaws.com/edith-typesparrow/admin-server
  newTag: sse-squad-hygiene-16jun2025-edith_d915971146
- name: api-server
  newName: ************.dkr.ecr.us-east-1.amazonaws.com/edith-typesparrow/api-server
  newTag: sse-squad-hygiene-04jul2025-edith_2e849bc162
- name: worker
  newName: ************.dkr.ecr.us-east-1.amazonaws.com/edith-typesparrow/worker
  newTag: sse-squad-hygiene-04jul2025-edith_2e849bc163
patches:
- path: ingress.yaml
- path: mailpit_pvc.yaml
- path: mailpit_node_selector.yaml
- path: deployment_patch.yaml
  target:
    group: apps
    kind: Deployment
    labelSelector: service=edith
    version: v1
- path: service_account_patch.yaml
