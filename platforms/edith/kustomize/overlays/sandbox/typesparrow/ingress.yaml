apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/healthcheck-path: /check/health
    alb.ingress.kubernetes.io/target-node-labels: app=edith-typesparrow
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=1200,
      deletion_protection.enabled=true,
      access_logs.s3.enabled=true,
      access_logs.s3.bucket=ss-squad-alb-logs,
      access_logs.s3.prefix=ss-microservices-alb
spec:
  rules:
    - host: edith.typesparrow.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: api-server-service
                port:
                  number: 8080
    - host: stark.typesparrow.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: admin-server-service
                port:
                  number: 8080
    - host: inbox.typesparrow.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: mailpit-service
                port:
                  number: 8025
