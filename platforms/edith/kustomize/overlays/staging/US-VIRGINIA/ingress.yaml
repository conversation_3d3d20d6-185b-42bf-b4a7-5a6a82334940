apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/healthcheck-path: /check/health
    alb.ingress.kubernetes.io/target-node-labels: app=edith
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=1200,
      deletion_protection.enabled=true,
      access_logs.s3.enabled=true,
      access_logs.s3.bucket=ss-squad-alb-logs,
      access_logs.s3.prefix=ss-microservices-alb
spec:
  rules:
    - host: edith-api.salesparrow.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: api-server-service
                port:
                  number: 8080
    - host: stark.salesparrow.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: admin-server-service
                port:
                  number: 8080
    - host: inbox.salesparrow.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: mailpit-service-a
                port:
                  number: 8025
    - host: thrive-inbox.salesparrow.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: mailpit-service-b
                port:
                  number: 8025
