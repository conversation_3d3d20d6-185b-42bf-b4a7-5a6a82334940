apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:974682937630:certificate/0ce41153-15c4-4fe3-bd52-93bd6fd10b3c
    alb.ingress.kubernetes.io/healthcheck-path: /check/health
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=1200,
      deletion_protection.enabled=true,
      access_logs.s3.enabled=true,
      access_logs.s3.bucket=ss-us-alb-logs,
      access_logs.s3.prefix=edith
    alb.ingress.kubernetes.io/target-node-labels: app=edith
    alb.ingress.kubernetes.io/load-balancer-name: ss-edith-alb
    alb.ingress.kubernetes.io/group.name: ss-edith-ingress-group
    alb.ingress.kubernetes.io/tags: CreatedBy=<EMAIL>,Team=Hygiene,Service=Edith
spec:
  rules:
    - host: api.sparrowmailer.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: api-server-service
                port:
                  number: 8080
