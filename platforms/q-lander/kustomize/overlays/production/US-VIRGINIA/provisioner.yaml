apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: np
spec:
  disruption:
    budgets:
    - nodes: 10%
    consolidateAfter: 0s
    consolidationPolicy: WhenEmptyOrUnderutilized
  limits:
    cpu: "100"
    memory: 200Gi
  template:
    metadata:
      annotations:
        CreatedBy: <EMAIL>
        Name: q-lander
        NodePool: q-lander
      labels:
        app: q-lander
    spec:
      expireAfter: 720h
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: us-virginia-q-lander-nc
      requirements:
      - key: karpenter.sh/capacity-type
        operator: In
        values:
        - on-demand
      - key: node.kubernetes.io/instance-type
        operator: In
        values:
        - t4g.micro
        - t4g.small
      - key: topology.kubernetes.io/zone
        operator: In
        values:
        - us-east-1a
      - key: kubernetes.io/arch
        operator: In
        values:
        - arm64
      - key: kubernetes.io/os
        operator: In
        values:
        - linux
      taints:
      - effect: NoSchedule
        key: app
        value: platforms-reserved
---
apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: nc
spec:
  amiSelectorTerms:
  - alias: al2@latest
  metadataOptions:
    httpEndpoint: enabled
    httpProtocolIPv6: disabled
    httpPutResponseHopLimit: 2
    httpTokens: required
  role: KarpenterNodeRole-platform-production-cluster
  securityGroupSelectorTerms:
  - tags:
      PlatformsK8s: "true"
  subnetSelectorTerms:
  - tags:
      PlatformsK8s: "true"
  tags:
    CreatedBy: <EMAIL>
    IntentLabel: app
    KarpenterProvisionerName: q-lander
    Name: Karpenter/QLander
    Service: QLander
    Team: Hygiene
    app: QLander
    CostCenter: CC-QLander
    ManagedBy: Karpenter
    Environment: production
    intent: app
