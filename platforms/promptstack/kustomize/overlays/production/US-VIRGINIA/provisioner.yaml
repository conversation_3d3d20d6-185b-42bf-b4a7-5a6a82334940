apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: np
spec:
  disruption:
    budgets:
    - nodes: 10%
    consolidateAfter: 0s
    consolidationPolicy: WhenEmptyOrUnderutilized
  limits:
    cpu: 1k
    memory: 1000Gi
  template:
    metadata:
      labels:
        app: promptstack
        intent: app
    spec:
      expireAfter: 720h
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: us-virginia-promptstack-nc
      requirements:
      - key: karpenter.sh/capacity-type
        operator: In
        values:
        - on-demand
      - key: node.kubernetes.io/instance-type
        operator: In
        values:
        - t4g.small
        - t4g.medium
      - key: kubernetes.io/arch
        operator: In
        values:
        - arm64
      - key: topology.kubernetes.io/zone
        operator: In
        values:
        - us-east-1a
        - us-east-1b
        - us-east-1c
        - us-east-1d
        - us-east-1f
      - key: kubernetes.io/os
        operator: In
        values:
        - linux
      taints:
      - effect: NoSchedule
        key: app
        value: platforms-reserved
---
apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: nc
spec:
  amiSelectorTerms:
  - alias: al2@latest
  metadataOptions:
    httpEndpoint: enabled
    httpProtocolIPv6: disabled
    httpPutResponseHopLimit: 2
    httpTokens: required
  role: KarpenterNodeRole-platform-production-cluster
  securityGroupSelectorTerms:
  - tags:
      PlatformsK8s: "true"
  subnetSelectorTerms:
  - tags:
      PlatformsK8s: "true"
  tags:
    CreatedBy: <EMAIL>
    IntentLabel: app
    KarpenterProvisionerName: promptstack
    Name: Karpenter/Promptstack
    Service: Promptstack
    Team: Anchor
    CostCenter: CC-Promptstack
    ManagedBy: Karpenter
    Environment: production
    app: promptstack
    intent: app
