apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/certificate-arn: >-
      arn:aws:acm:us-east-1:288761754283:certificate/805dadf4-333e-4ae4-a009-1cb360c1c7aa
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/listen-ports: >-
      [{"HTTP": 80}, {"HTTPS":443}]
    alb.ingress.kubernetes.io/actions.ssl-redirect: >-
      {"Type": "redirect", "RedirectConfig": { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}
    alb.ingress.kubernetes.io/healthcheck-protocol: HTTP
    alb.ingress.kubernetes.io/healthcheck-path: /haproxy/monitor
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '30'
    alb.ingress.kubernetes.io/healthcheck-timeout-seconds: '10'
    alb.ingress.kubernetes.io/success-codes: '200'
    alb.ingress.kubernetes.io/healthy-threshold-count: '2'
    alb.ingress.kubernetes.io/unhealthy-threshold-count: '2'
    alb.ingress.kubernetes.io/subnets: >-
      subnet-02f726e7d453386b7,
      subnet-058b8d3f5eb2e4cf1,
      subnet-006d0293d40956eac,
      subnet-0265b8262120a5de6,
      subnet-016aca31084be5ede,
      subnet-00ccca1c406bb11ad
    alb.ingress.kubernetes.io/security-groups: sg-025a2e12e63f87155
    alb.ingress.kubernetes.io/load-balancer-name: sd-app-prod-alb
    alb.ingress.kubernetes.io/group.name: sd-app-prod-ingress-group
    alb.ingress.kubernetes.io/load-balancer-attributes: >-
      deletion_protection.enabled=true,idle_timeout.timeout_seconds=1200
spec:
  ingressClassName: my-aws-ingress-class
  rules: []
