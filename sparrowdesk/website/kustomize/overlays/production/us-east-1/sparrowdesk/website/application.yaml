apiVersion: apps/v1
kind: Deployment
metadata:
  name: deployment
spec:
  selector:
    matchLabels:
      app: sparrowdesk-website
  template:
    metadata:
      labels:
        app: sparrowdesk-website
    spec:
      nodeSelector:
        app: website-arm
        intent: website
      tolerations:
        - key: "app"
          operator: "Equal"
          value: "website"
          effect: "NoSchedule"
      volumes:
        - name: secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: sparrowdesk-secrets
      serviceAccountName: sparrowdesk-sa
      containers:
        - name: website
          securityContext:
            runAsNonRoot: true
          envFrom:
            - configMapRef:
                name: sparrowdesk-configmap
            - secretRef:
                name: sparrowdesk-secrets
