apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: sparrowdesk-website
namePrefix: sparrowdesk-
labels:
- pairs:
    layer: sparrowdesk
    environment: production
resources:
- cms
- website
- secrets
- *****************:surveysparrow/sparrowdesk-production-config.git//website?ref=main
- ../../../../../../ingress-base/production/http-us-east-1/
- arm_karpenter.yaml
- service-account.yaml
- namespace.yaml
images:
- name: website
  newName: ************.dkr.ecr.us-east-1.amazonaws.com/sd-website
  newTag: legal-page_2e0ae4a_89
patches:
- path: ingress-patch.yaml
