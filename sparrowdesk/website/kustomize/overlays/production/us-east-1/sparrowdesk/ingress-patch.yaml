apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/healthcheck-path: /
    alb.ingress.kubernetes.io/certificate-arn: >-
      arn:aws:acm:us-east-1:288761754283:certificate/805dadf4-333e-4ae4-a009-1cb360c1c7aa
    alb.ingress.kubernetes.io/load-balancer-name: sd-website-prod-alb
    alb.ingress.kubernetes.io/group.name: sd-website-prod-ingress-group
spec:
  ingressClassName: my-aws-ingress-class
  defaultBackend:
    service:
      name: sparrowdesk-website-service
      port:
        number: 3000
  rules:
    - http:
        paths:
          - path: /admin
            pathType: Prefix
            backend:
              service:
                name: sparrowdesk-cms-service
                port:
                  number: 3000
          - path: /
            pathType: Prefix
            backend:
              service:
                name: sparrowdesk-website-service
                port:
                  number: 3000
