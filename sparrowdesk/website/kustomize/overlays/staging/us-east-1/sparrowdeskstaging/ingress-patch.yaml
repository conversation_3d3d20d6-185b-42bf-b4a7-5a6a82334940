apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/healthcheck-path: /
    alb.ingress.kubernetes.io/certificate-arn: >-
      arn:aws:acm:us-east-1:396913698328:certificate/73bf3ab3-a4fe-4d03-b9b4-1a83c7d4e01a
spec:
  rules:
    - host: "www.campaignsparrow.com"
      http:
        paths:
          - path: /admin
            pathType: Prefix
            backend:
              service:
                name: sparrowdeskstaging-cms-service
                port:
                  number: 3000
          - path: /
            pathType: Prefix
            backend:
              service:
                name: sparrowdeskstaging-website-service
                port:
                  number: 3000
