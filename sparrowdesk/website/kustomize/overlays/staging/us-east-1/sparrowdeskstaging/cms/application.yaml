apiVersion: apps/v1
kind: Deployment
metadata:
  name: deployment
spec:
  selector:
    matchLabels:
      app: sparrowdeskstaging-cms
  template:
    metadata:
      labels:
        app: sparrowdeskstaging-cms
    spec:
      nodeSelector:
        app: website-arm
        intent: website
      tolerations:
        - key: "app"
          operator: "Equal"
          value: "website"
          effect: "NoSchedule"
      volumes:
        - name: secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: sparrowdeskstaging-secrets
      serviceAccountName: sparrowdeskstaging-sa
      containers:
        - name: website
          securityContext:
            runAsNonRoot: true
          envFrom:
            - configMapRef:
                name: sparrowdeskstaging-configmap
            - secretRef:
                name: sparrowdeskstaging-secrets
