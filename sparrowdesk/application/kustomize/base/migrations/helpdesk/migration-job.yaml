apiVersion: batch/v1
kind: Job
metadata:
  name: helpdesk-${MIGRATION_TYPE}-migration
  namespace: ${NAMESPACE}
  labels:
    migration_code: ${MIGRATION_TYPE}-helpdesk-migration-${BUILD_NUMBER}
spec:
  template:
    metadata:
      labels:
        app: helpdesk
        migration_code: ${MIGRATION_TYPE}-helpdesk-migration-${BUILD_NUMBER}
    spec:
      restartPolicy: Never
      containers:
        - name: helpdesk
          command: ["/bin/sh", "-c"]
          args: ["npm run ${MIGRATION_COMMAND}"]
          envFrom:
            - secretRef:
                name: ${PREFIX}-helpdesk-secrets
            - configMapRef:
                name: ${PREFIX}-helpdesk-migration-configmap
          image: ${IMAGE}:${TAG}
          imagePullPolicy: Always
          resources:
            requests:
              cpu: "1000m"
              memory: "1.5Gi"
            limits:
              memory: "1.5Gi"
          volumeMounts:
            - mountPath: /app/application/helpdesk-application/config/env/${ENV}.json
              name: app-config
              subPath: ${ENV}.json
            - mountPath: /mnt/secrets
              name: secrets-store-inline
              readOnly: true
      nodeSelector:
        app: ${APP_NAME}-arm
        intent: app
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - helpdesk
                topologyKey: kubernetes.io/hostname
      serviceAccountName: ${PREFIX}-helpdesk-sa
      tolerations:
        - effect: NoSchedule
          operator: Equal
          key: app
          value: ${TOLERATION_VALUE}-app
      volumes:
        - configMap:
            defaultMode: 420
            items:
              - key: ${ENV}.json
                path: ${ENV}.json
            name: ${PREFIX}-helpdesk-migration-configmap
          name: app-config
        - csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: ${PREFIX}-helpdesk-secrets
          name: secrets-store-inline
  ttlSecondsAfterFinished: 7200  # Cleanup Jobs after 2 hour
  backoffLimit: 0
