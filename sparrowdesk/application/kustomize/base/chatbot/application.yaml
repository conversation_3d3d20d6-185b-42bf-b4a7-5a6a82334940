apiVersion: apps/v1
kind: Deployment
metadata:
  name: chatbot-application
spec:
  selector:
    matchLabels:
      app: chatbot
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  template:
    metadata:
      labels:
        app: chatbot
    spec:
      volumes:
      - name: secrets-store-inline
        csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: secrets # Patch this to the secret provider class name in the overlay
      terminationGracePeriodSeconds: 30
      serviceAccountName: chatbot-sa
      containers:
      - name: chatbot-app
        image: chatbot:app
        imagePullPolicy: IfNotPresent
        args: ["dist/chatbot-application/index.js"]
        securityContext:
          runAsUser: 1000
          runAsGroup: 3000
        lifecycle:
          preStop:
            exec:
              command: ["/bin/sh", "-c", "sleep 5"]
        volumeMounts:
        - name: secrets-store-inline
          mountPath: /mnt/secrets
          readOnly: true
        readinessProbe:
          httpGet:
            path: /check/health
            port: 9000
          initialDelaySeconds: 30
          timeoutSeconds: 15
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 2
        livenessProbe:
          httpGet:
            path: /check/health
            port: 9000
          initialDelaySeconds: 40
          timeoutSeconds: 15
          periodSeconds: 60
          failureThreshold: 5
          successThreshold: 1
        ports:
        - containerPort: 9000
