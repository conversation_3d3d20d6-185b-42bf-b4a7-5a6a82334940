apiVersion: apps/v1
kind: Deployment
metadata:
  name: websocket-application
spec:
  selector:
    matchLabels:
      app: websocket
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  template:
    metadata:
      labels:
        app: websocket
    spec:
      terminationGracePeriodSeconds: 30
      serviceAccountName: websocket-sa
      securityContext:
        runAsUser: 1000
        runAsGroup: 3000
      containers:
      - name: websocket-app
        image: websocket:app
        imagePullPolicy: IfNotPresent
        args: ["dist/src/index.js"]
        lifecycle:
          preStop:
            exec:
              command: ["/bin/sh", "-c", "sleep 5"]
        readinessProbe:
          httpGet:
            path: /
            port: 8800
          initialDelaySeconds: 30
          timeoutSeconds: 15
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 2
        livenessProbe:
          httpGet:
            path: /
            port: 8800
          initialDelaySeconds: 40
          timeoutSeconds: 15
          periodSeconds: 60
          failureThreshold: 5
          successThreshold: 1
        ports:
        - containerPort: 8800
