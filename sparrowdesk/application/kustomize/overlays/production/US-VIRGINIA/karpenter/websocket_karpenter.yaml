apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: websocket-arm
spec:
  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
    consolidateAfter: 3m
  limits:
    cpu: "500"
    memory: 1000Gi
  template:
    metadata:
      labels:
        app: websocket-arm
        intent: app
    spec:
      nodeClassRef:
        name: sd-us-vi-websocket-arm-nc
        kind: EC2NodeClass
        group: karpenter.k8s.aws

      taints:
        - key: app
          value: websocket-app
          effect: NoSchedule
      requirements:
        - key: karpenter.sh/capacity-type
          operator: In
          values:
            - on-demand
        - key: node.kubernetes.io/instance-type
          operator: In
          values:
            - t4g.micro
            - t4g.small
            - t4g.medium
        - key: topology.kubernetes.io/zone
          operator: In
          values:
            - us-east-1a
            - us-east-1b
            - us-east-1c
            - us-east-1d
            - us-east-1f
        - key: kubernetes.io/os
          operator: In
          values:
            - linux
        - key: kubernetes.io/arch
          operator: In
          values:
            - arm64
---
apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: websocket-arm-nc
spec:
  kubelet:
    podsPerCore: 12
    maxPods: 20
    systemReserved:
      cpu: 100m
      memory: 100Mi
      ephemeral-storage: 1Gi
    kubeReserved:
      cpu: 200m
      memory: 100Mi
      ephemeral-storage: 3Gi
    evictionHard:
      memory.available: 5%
      nodefs.available: 10%
      nodefs.inodesFree: 10%
    evictionSoft:
      memory.available: 500Mi
      nodefs.available: 15%
      nodefs.inodesFree: 15%
    evictionSoftGracePeriod:
      memory.available: 1m
      nodefs.available: 1m30s
      nodefs.inodesFree: 2m
    evictionMaxPodGracePeriod: 60
    imageGCHighThresholdPercent: 85
    imageGCLowThresholdPercent: 80
    cpuCFSQuota: true
  amiFamily: AL2023
  blockDeviceMappings:
    - deviceName: /dev/xvda
      ebs:
        volumeSize: 21Gi
        volumeType: gp3
        iops: 3000
        encrypted: true
        kmsKeyID: "e6ca94b1-db2f-497a-9415-0016d20bfbd4"
        deleteOnTermination: true
        throughput: 125

  # need to check for feasibility to maintain same name in all surveysparrow cluster and environment
  role: KarpenterProductionAppNodeRole
  amiSelectorTerms:
    - id: ami-0b2c365e63b0b91c1 # arm64
    - id: ami-088c76cb634d5f491 # x86_64
  securityGroupSelectorTerms:
    - tags:
        karpenter.sh/discovery: "sd-production-cluster"
  subnetSelectorTerms:
    - tags:
        karpenter.sh/discovery: "sd-production-cluster"
  tags:
    karpenter.sh/discovery: "sd-production-cluster"
    Environment: "production"
    CostCenter: "CC-SparrowDesk"
    Application: "sd-us-vi-websocket"
    KarpenterProvisionerName: "websocket"
    map-migrated: "migB3CDHXH2I"
    ManagedBy: "Karpenter"
    CreatedBy: "<EMAIL>"
    IntentLabel: "app"
    Name: "Karpenter/sd-us-vi-websocket"
  metadataOptions:
    httpEndpoint: enabled
    httpProtocolIPv6: enabled
    httpPutResponseHopLimit: 3
    httpTokens: required
  userData: |
    echo "Hello world"

  detailedMonitoring: false

  associatePublicIPAddress: false
