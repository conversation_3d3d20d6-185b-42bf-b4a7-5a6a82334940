apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
- ../../../../base/helpdesk
- ../../../../base/helpdesk/high-availability
- secrets.yaml
images:
- name: helpdesk:app
  newName: ************.dkr.ecr.us-east-1.amazonaws.com/sd-us-vi-helpdesk-backend
  newTag: helpdesk_main_72698b1e_67
- name: helpdesk:worker
  newName: ************.dkr.ecr.us-east-1.amazonaws.com/sd-us-vi-helpdesk-backend
  newTag: helpdesk_main_72698b1e_67
patches:
- path: application_pdb.yaml
- path: application_patch.yaml
- path: application_replicas.yaml
- path: service_account.yaml
- path: worker_patch.yaml
  target:
    kind: Deployment
    name: helpdesk-worker
