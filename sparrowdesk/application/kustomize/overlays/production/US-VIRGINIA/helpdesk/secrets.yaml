apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: helpdesk-secrets
spec:
  provider: aws
  parameters:
    objects: |
      - objectName: "sd-us-vi-secrets"
        objectType: "secretsmanager"
        jmesPath:

          - path: HELPDESK_ACCESS_KEY_ID
            objectAlias: ACCESS_KEY_ID-Alias

          - path: HELPDESK_SECRET_KEY_ID
            objectAlias: SECRET_KEY_ID-Alias

          - path: HELPDESK_NODE_ENV
            objectAlias: NODE_ENV-Alias

          - path: HELPDESK_VERSION
            objectAlias: VERSION-Alias

          - path: HELPDESK_JWT_SECRET
            objectAlias: JWT_SECRET-Alias

          - path: HELPDESK_DB_SHARD_1_USERNAME
            objectAlias: DB_SHARD_1_USERNAME-Alias

          - path: HELPDESK_DB_SHARD_1_PASSWORD
            objectAlias: DB_SHARD_1_PASSWORD-Alias

          - path: HELPDESK_DB_SHARD_2_USERNAME
            objectAlias: DB_SHARD_2_USERNAME-Alias

          - path: HELPDESK_DB_SHARD_2_PASSWORD
            objectAlias: DB_SHARD_2_PASSWORD-Alias

          - path: HELPDESK_SHARDS_RDS_PROXY_READ_USERNAME
            objectAlias: SHARDS_RDS_PROXY_READ_USERNAME-Alias

          - path: HELPDESK_SHARDS_RDS_PROXY_READ_PASSWORD
            objectAlias: SHARDS_RDS_PROXY_READ_PASSWORD-Alias

          - path: HELPDESK_SHARDS_RDS_PROXY_WRITE_USERNAME
            objectAlias: SHARDS_RDS_PROXY_WRITE_USERNAME-Alias

          - path: HELPDESK_SHARDS_RDS_PROXY_WRITE_PASSWORD
            objectAlias: SHARDS_RDS_PROXY_WRITE_PASSWORD-Alias

          - path: HELPDESK_OPENSEARCH_USERNAME
            objectAlias: OPENSEARCH_USERNAME-Alias

          - path: HELPDESK_OPENSEARCH_PASSWORD
            objectAlias: OPENSEARCH_PASSWORD-Alias

          - path: HELPDESK_SHIELD_TOKEN
            objectAlias: SHIELD_TOKEN-Alias

          - path: HELPDESK_MAIL_GUN_EMAIL_KEY
            objectAlias: MAIL_GUN_EMAIL_KEY-Alias

          - path: HELPDESK_MAILGUN_PRIVATE_KEY
            objectAlias: MAILGUN_PRIVATE_KEY-Alias

          - path: HELPDESK_MAILGUN_PUBLIC_KEY
            objectAlias: MAILGUN_PUBLIC_KEY-Alias

          - path: HELPDESK_SEND_GRID_EMAIL_KEY
            objectAlias: SEND_GRID_EMAIL_KEY-Alias

          - path: HELPDESK_SPARKPOST_EMAIL_KEY
            objectAlias: SPARKPOST_EMAIL_KEY-Alias

          - path: HELPDESK_SPARKPOST_PUBLIC_KEY
            objectAlias: SPARKPOST_PUBLIC_KEY-Alias

          - path: HELPDESK_SPARKPOST_PRIVATE_KEY
            objectAlias: SPARKPOST_PRIVATE_KEY-Alias

          - path: HELPDESK_TWILIO_API_KEY
            objectAlias: TWILIO_API_KEY-Alias

          - path: HELPDESK_TWILIO_SID
            objectAlias: TWILIO_SID-Alias

          - path: HELPDESK_TWILIO_LOGIN_VERIFY_SERVICE_SID
            objectAlias: TWILIO_LOGIN_VERIFY_SERVICE_SID-Alias

          - path: HELPDESK_TWILIO_SIGNUP_VERIFY_SERVICE_SID
            objectAlias: TWILIO_SIGNUP_VERIFY_SERVICE_SID-Alias

          - path: HELPDESK_DEBOUNCE_EMAIL_SHARE_API_KEY
            objectAlias: DEBOUNCE_EMAIL_SHARE_API_KEY-Alias

          - path: HELPDESK_DEBOUNCE_SIGNUP_API_KEY
            objectAlias: DEBOUNCE_SIGNUP_API_KEY-Alias

          - path: HELPDESK_SESSION_PASSWORD
            objectAlias: SESSION_PASSWORD-Alias

          - path: HELPDESK_QUICK_EMAIL_VERIFICATION_API_KEY
            objectAlias: QUICK_EMAIL_VERIFICATION_API_KEY-Alias

          - path: HELPDESK_CLOUDFLARE_API_TOKEN
            objectAlias: CLOUDFLARE_API_TOKEN-Alias

          - path: HELPDESK_CLOUDFLARE_ZONE_ID
            objectAlias: CLOUDFLARE_ZONE_ID-Alias

          - path: HELPDESK_NEW_RELIC_LICENSE_KEY
            objectAlias: NEW_RELIC_LICENSE_KEY-Alias

          - path: HELPDESK_NEW_RELIC_APP_NAME
            objectAlias: NEW_RELIC_APP_NAME-Alias

          - path: HELPDESK_SUPERSET_ACCESS_TOKEN
            objectAlias: SUPERSET_ACCESS_TOKEN-Alias

          - path: HELPDESK_SUPERSET_REFRESH_TOKEN
            objectAlias: SUPERSET_REFRESH_TOKEN-Alias

          - path: HELPDESK_IP_INFO_API_KEY
            objectAlias: IP_INFO_API_KEY-Alias

  secretObjects:
    - secretName: sd-us-vi-helpdesk-secrets
      type: Opaque
      data:
        - objectName: "ACCESS_KEY_ID-Alias"
          key: ACCESS_KEY_ID

        - objectName: "SECRET_KEY_ID-Alias"
          key: SECRET_KEY_ID

        - objectName: "NODE_ENV-Alias"
          key: NODE_ENV

        - objectName: "VERSION-Alias"
          key: VERSION

        - objectName: "JWT_SECRET-Alias"
          key: JWT_SECRET

        - objectName: "DB_SHARD_1_USERNAME-Alias"
          key: DB_SHARD_1_USERNAME

        - objectName: "DB_SHARD_1_PASSWORD-Alias"
          key: DB_SHARD_1_PASSWORD

        - objectName: "DB_SHARD_2_USERNAME-Alias"
          key: DB_SHARD_2_USERNAME

        - objectName: "DB_SHARD_2_PASSWORD-Alias"
          key: DB_SHARD_2_PASSWORD

        - objectName: "SHARDS_RDS_PROXY_READ_USERNAME-Alias"
          key: SHARDS_RDS_PROXY_READ_USERNAME

        - objectName: "SHARDS_RDS_PROXY_READ_PASSWORD-Alias"
          key: SHARDS_RDS_PROXY_READ_PASSWORD

        - objectName: "SHARDS_RDS_PROXY_WRITE_USERNAME-Alias"
          key: SHARDS_RDS_PROXY_WRITE_USERNAME

        - objectName: "SHARDS_RDS_PROXY_WRITE_PASSWORD-Alias"
          key: SHARDS_RDS_PROXY_WRITE_PASSWORD

        - objectName: "OPENSEARCH_USERNAME-Alias"
          key: OPENSEARCH_USERNAME

        - objectName: "OPENSEARCH_PASSWORD-Alias"
          key: OPENSEARCH_PASSWORD

        - objectName: "SHIELD_TOKEN-Alias"
          key: SHIELD_TOKEN

        - objectName: "MAIL_GUN_EMAIL_KEY-Alias"
          key: MAIL_GUN_EMAIL_KEY

        - objectName: "MAILGUN_PRIVATE_KEY-Alias"
          key: MAILGUN_PRIVATE_KEY

        - objectName: "MAILGUN_PUBLIC_KEY-Alias"
          key: MAILGUN_PUBLIC_KEY

        - objectName: "SEND_GRID_EMAIL_KEY-Alias"
          key: SEND_GRID_EMAIL_KEY

        - objectName: "SPARKPOST_EMAIL_KEY-Alias"
          key: SPARKPOST_EMAIL_KEY

        - objectName: "SPARKPOST_PUBLIC_KEY-Alias"
          key: SPARKPOST_PUBLIC_KEY

        - objectName: "SPARKPOST_PRIVATE_KEY-Alias"
          key: SPARKPOST_PRIVATE_KEY

        - objectName: "TWILIO_API_KEY-Alias"
          key: TWILIO_API_KEY

        - objectName: "TWILIO_SID-Alias"
          key: TWILIO_SID

        - objectName: "TWILIO_SIGNUP_VERIFY_SERVICE_SID-Alias"
          key: TWILIO_SIGNUP_VERIFY_SERVICE_SID

        - objectName: "TWILIO_LOGIN_VERIFY_SERVICE_SID-Alias"
          key: TWILIO_LOGIN_VERIFY_SERVICE_SID

        - objectName: "TWILIO_SIGNUP_VERIFY_SERVICE_SID-Alias"
          key: TWILIO_SIGNUP_VERIFY_SERVICE_SID

        - objectName: "DEBOUNCE_EMAIL_SHARE_API_KEY-Alias"
          key: DEBOUNCE_EMAIL_SHARE_API_KEY

        - objectName: "DEBOUNCE_SIGNUP_API_KEY-Alias"
          key: DEBOUNCE_SIGNUP_API_KEY

        - objectName: "SESSION_PASSWORD-Alias"
          key: SESSION_PASSWORD

        - objectName: "QUICK_EMAIL_VERIFICATION_API_KEY-Alias"
          key: QUICK_EMAIL_VERIFICATION_API_KEY

        - objectName: "CLOUDFLARE_API_TOKEN-Alias"
          key: CLOUDFLARE_API_TOKEN

        - objectName: "CLOUDFLARE_ZONE_ID-Alias"
          key: CLOUDFLARE_ZONE_ID

        - objectName: "NEW_RELIC_LICENSE_KEY-Alias"
          key: NEW_RELIC_LICENSE_KEY

        - objectName: "NEW_RELIC_APP_NAME-Alias"
          key: NEW_RELIC_APP_NAME

        - objectName: "SUPERSET_ACCESS_TOKEN-Alias"
          key: SUPERSET_ACCESS_TOKEN

        - objectName: "SUPERSET_REFRESH_TOKEN-Alias"
          key: SUPERSET_REFRESH_TOKEN

        - objectName: "IP_INFO_API_KEY-Alias"
          key: IP_INFO_API_KEY
