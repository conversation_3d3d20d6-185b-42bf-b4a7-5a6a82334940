apiVersion: apps/v1
kind: Deployment
metadata:
  name: helpdesk-worker
  labels:
    app: helpdesk-worker
    environment: production
spec:
  selector:
    matchLabels:
      app: helpdesk-worker
  template:
    metadata:
      labels:
        app: helpdesk-worker
        environment: production
    spec:
      nodeSelector:
        app: helpdesk-worker-arm
        intent: app
      tolerations:
        - key: "app"
          operator: "Equal"
          value: "helpdesk-worker-app"
          effect: "NoSchedule"
      volumes:
        - name: app-config
          configMap:
            name: sd-us-vi-helpdesk-configmap
            items:
              - key: production.json
                path: production.json
            defaultMode: 420
        - name: secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: sd-us-vi-helpdesk-secrets
      containers:
        - name: helpdesk-worker
          envFrom:
            - configMapRef:
                name: sd-us-vi-helpdesk-worker-configmap
            - configMapRef:
                name: sd-us-vi-helpdesk-configmap
            - secretRef:
                name: sd-us-vi-helpdesk-secrets
          resources:
            requests:
              cpu: "500m"
              memory: "1000Mi"
            limits:
              memory: "1000Mi"
          volumeMounts:
            - name: app-config
              mountPath: /app/application/helpdesk-application/config/env/production.json
              subPath: production.json
