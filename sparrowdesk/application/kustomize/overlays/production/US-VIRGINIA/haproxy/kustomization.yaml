apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
  - ../../../../base/haproxy
  - ../../../../base/haproxy/high-availability
  - ../../../../../../ingress-base/production/http-us-east-1/

labels:
  - pairs:
      app: haproxy
      environment: production

images:
  - name: haproxy-image
    newName: public.ecr.aws/docker/library/haproxy
    newTag: "3.0"

patches:
  - path: ingress.yaml
  - path: application_replicas.yaml
  - path: application_patch.yaml
  - path: application_pdb.yaml
