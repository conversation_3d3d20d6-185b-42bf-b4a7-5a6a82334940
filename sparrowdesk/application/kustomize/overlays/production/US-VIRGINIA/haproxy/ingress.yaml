apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: >-
      arn:aws:acm:us-east-1:288761754283:certificate/805dadf4-333e-4ae4-a009-1cb360c1c7aa
spec:
  rules:
    - host: "*.sparrowdesk.com"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ssl-redirect
                port:
                  name: use-annotation
    - host: "us-sd-rtn.sparrowdesk.com"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: sd-us-vi-websocket-application-service
                port:
                  number: 8800
    - host: "*.sparrowdesk.com"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: sd-us-vi-haproxy-service
                port:
                  number: 8081
