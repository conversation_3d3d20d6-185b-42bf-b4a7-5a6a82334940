apiVersion: apps/v1
kind: Deployment
metadata:
  name: websocket-application
spec:
  template:
    spec:
      nodeSelector:
        app: websocket-arm
        intent: app
      tolerations:
        - key: "app"
          operator: "Equal"
          value: "websocket-app"
          effect: "NoSchedule"
      containers:
        - name: websocket-app
          envFrom:
            - configMapRef:
                name: sd-us-vi-websocket-configmap
          resources:
            requests:
              cpu: 1000m
              memory: 2000Mi
            limits:
              memory: 2000Mi
