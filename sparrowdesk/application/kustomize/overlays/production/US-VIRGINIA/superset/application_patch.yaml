apiVersion: apps/v1
kind: Deployment
metadata:
  name: superset-application
spec:
  template:
    spec:
      nodeSelector:
        app: superset-arm
        intent: app
      tolerations:
        - key: "app"
          operator: "Equal"
          value: "superset-app"
          effect: "NoSchedule"
      volumes:
        - name: sd-us-vi-superset-configmap
          configMap:
            name: sd-us-vi-superset-configmap
            defaultMode: 420
        - name: sd-us-vi-superset-bootstrap-configmap
          configMap:
            name: sd-us-vi-superset-bootstrap-configmap
            defaultMode: 420
        - name: secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: sd-us-vi-superset-secrets
        - name: superset-config
          configMap:
            name: superset-config
            defaultMode: 420
      containers:
        - name: superset-app
          ports:
            - name: http
              containerPort: 8088
              protocol: TCP
          envFrom:
            - configMapRef:
                name: sd-us-vi-superset-env-configmap
            - secretRef:
                name: sd-us-vi-superset-secrets
          env:
            - name: SUPERSET_PORT
              value: '8088'
          resources:
            requests:
              cpu: 500m
              memory: 1000Mi
            limits:
              memory: 1000Mi
          volumeMounts:
            - name: sd-us-vi-superset-configmap
              readOnly: true
              mountPath: /app/pythonpath/superset_config.py
              subPath: superset_config.py
            - name: sd-us-vi-superset-bootstrap-configmap
              readOnly: true
              mountPath: /app/pythonpath/superset_bootstrap.sh
              subPath: superset_bootstrap.sh
            - name: secrets-store-inline
              readOnly: true
              mountPath: /mnt/secrets
