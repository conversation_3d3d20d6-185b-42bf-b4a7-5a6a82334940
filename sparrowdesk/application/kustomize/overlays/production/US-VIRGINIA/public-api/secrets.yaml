apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: public-api-secrets
spec:
  provider: aws
  parameters:
    objects: |
      - objectName: "sd-us-vi-secrets"
        objectType: "secretsmanager"
        jmesPath:

          - path: PUBLIC_API_POSTGRES_SHARD_1_USERNAME
            objectAlias: POSTGRES_SHARD_1_USERNAME-Alias

          - path: PUBLIC_API_POSTGRES_SHARD_1_PASSWORD
            objectAlias: POSTGRES_SHARD_1_PASSWORD-Alias

          - path: PUBLIC_API_POSTGRES_SHARD_2_USERNAME
            objectAlias: POSTGRES_SHARD_2_USERNAME-Alias

          - path: PUBLIC_API_POSTGRES_SHARD_2_PASSWORD
            objectAlias: POSTGRES_SHARD_2_PASSWORD-Alias

          - path: PUBLIC_API_OPENSEARCH_USERNAME
            objectAlias: OPENSEARCH_USERNAME-Alias

          - path: PUBLIC_API_OPENSEARCH_PASSWORD
            objectAlias: OPENSEARCH_PASSWORD-Alias

  secretObjects:
    - secretName: sd-us-vi-public-api-secrets
      type: Opaque
      data:
        - objectName: "POSTGRES_SHARD_1_USERNAME-Alias"
          key: POSTGRES_SHARD_1_USERNAME

        - objectName: "POSTGRES_SHARD_1_PASSWORD-Alias"
          key: POSTGRES_SHARD_1_PASSWORD

        - objectName: "POSTGRES_SHARD_2_USERNAME-Alias"
          key: POSTGRES_SHARD_2_USERNAME

        - objectName: "POSTGRES_SHARD_2_PASSWORD-Alias"
          key: POSTGRES_SHARD_2_PASSWORD

        - objectName: "OPENSEARCH_USERNAME-Alias"
          key: OPENSEARCH_USERNAME

        - objectName: "OPENSEARCH_PASSWORD-Alias"
          key: OPENSEARCH_PASSWORD
