apiVersion: apps/v1
kind: Deployment
metadata:
  name: chatbot-application
spec:
  template:
    spec:
      nodeSelector:
        app: chatbot-arm
        intent: app
      tolerations:
        - key: "app"
          operator: "Equal"
          value: "chatbot-app"
          effect: "NoSchedule"
      volumes:
        - name: secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: sd-us-vi-chatbot-secrets
      containers:
        - name: chatbot-app
          envFrom:
            - configMapRef:
                name: sd-us-vi-chatbot-configmap
            - secretRef:
                name: sd-us-vi-chatbot-secrets
          resources:
            requests:
              cpu: 1000m
              memory: 2000Mi
            limits:
              memory: 2000Mi
