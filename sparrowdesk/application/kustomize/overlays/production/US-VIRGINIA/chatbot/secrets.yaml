apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: chatbot-secrets
spec:
  provider: aws
  parameters:
    objects: |
      - objectName: "sd-us-vi-secrets"
        objectType: "secretsmanager"
        jmesPath:

          - path: CHATBOT_POSTGRES_SHARD_1_USERNAME
            objectAlias: POSTGRES_SHARD_1_USERNAME-Alias

          - path: CHATBOT_POSTGRES_SHARD_1_PASSWORD
            objectAlias: POSTGRES_SHARD_1_PASSWORD-Alias

          - path: CHATBOT_POSTGRES_SHARD_2_USERNAME
            objectAlias: POSTGRES_SHARD_2_USERNAME-Alias

          - path: CHATBOT_POSTGRES_SHARD_2_PASSWORD
            objectAlias: POSTGRES_SHARD_2_PASSWORD-Alias

          - path: CHATBOT_IP_INFO_API_KEY
            objectAlias: IP_INFO_API_KEY-Alias

  secretObjects:
    - secretName: sd-us-vi-chatbot-secrets
      type: Opaque
      data:
        - objectName: "POSTGRES_SHARD_1_USERNAME-Alias"
          key: POSTGRES_SHARD_1_USERNAME

        - objectName: "POSTGRES_SHARD_1_PASSWORD-Alias"
          key: POSTGRES_SHARD_1_PASSWORD

        - objectName: "POSTGRES_SHARD_2_USERNAME-Alias"
          key: POSTGRES_SHARD_2_USERNAME

        - objectName: "POSTGRES_SHARD_2_PASSWORD-Alias"
          key: POSTGRES_SHARD_2_PASSWORD

        - objectName: "IP_INFO_API_KEY-Alias"
          key: IP_INFO_API_KEY
