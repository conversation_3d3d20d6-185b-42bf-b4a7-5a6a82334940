apiVersion: apps/v1
kind: Deployment
metadata:
  name: public-api
spec:
  template:
    spec:
      nodeSelector:
        app: public-api-arm
        intent: app
      tolerations:
        - key: "app"
          operator: "Equal"
          value: "public-api-app"
          effect: "NoSchedule"
      volumes:
        - name: secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: berlin-public-api-secrets
      containers:
        - name: public-api-app
          envFrom:
            - configMapRef:
                name: berlin-public-api-configmap
            - secretRef:
                name: berlin-public-api-secrets
          resources:
            requests:
              cpu: 300m
              memory: 600Mi
            limits:
              memory: 600Mi
