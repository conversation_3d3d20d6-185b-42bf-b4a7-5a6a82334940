apiVersion: apps/v1
kind: Deployment
metadata:
  name: helpdesk-application
  labels:
    app: helpdesk
    environment: staging
spec:
  selector:
    matchLabels:
      app: helpdesk
  template:
    metadata:
      labels:
        app: helpdesk
        environment: staging
    spec:
      nodeSelector:
        app: helpdesk-arm
        intent: app
      tolerations:
      - key: app
        operator: Equal
        value: helpdesk-app
        effect: NoSchedule
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app
                operator: In
                values:
                - helpdesk
            topologyKey: kubernetes.io/hostname
      volumes:
      - name: app-config
        configMap:
          name: berlin-helpdesk-configmap
          items:
          - key: staging.json
            path: staging.json
          defaultMode: 420
      - name: secrets-store-inline
        csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: berlin-helpdesk-secrets
      containers:
      - name: helpdesk-app
        envFrom:
        - configMapRef:
            name: berlin-helpdesk-configmap
        - secretRef:
            name: berlin-helpdesk-secrets
        env:
        - name: VERSION
          value: f3c9958b3ea3759fa4ebba266cfb15d9121da9f8_175
        resources:
          requests:
            cpu: 500m
            memory: 600Mi
          limits:
            cpu: 1000m
            memory: 600Mi
        volumeMounts:
        - name: app-config
          mountPath: /app/application/helpdesk-application/config/env/staging.json
          subPath: staging.json
