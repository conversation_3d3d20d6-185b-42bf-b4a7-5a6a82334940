apiVersion: apps/v1
kind: Deployment
metadata:
  name: helpdesk-worker
  labels:
    app: helpdesk-worker
    environment: staging
spec:
  selector:
    matchLabels:
      app: helpdesk-worker
  template:
    metadata:
      labels:
        app: helpdesk-worker
        environment: staging
    spec:
      nodeSelector:
        app: helpdesk-worker-arm
        intent: app
      tolerations:
        - key: "app"
          operator: "Equal"
          value: "helpdesk-worker-app"
          effect: "NoSchedule"
      volumes:
        - name: app-config
          configMap:
            name: berlin-helpdesk-configmap
            items:
              - key: staging.json
                path: staging.json
            defaultMode: 420
        - name: secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: berlin-helpdesk-secrets
      containers:
        - name: helpdesk-worker
          envFrom:
            - configMapRef:
                name: berlin-helpdesk-worker-configmap
            - configMapRef:
                name: berlin-helpdesk-configmap
            - secretRef:
                name: berlin-helpdesk-secrets
          resources:
            requests:
              cpu: "200m"
              memory: "500Mi"
            limits:
              memory: "500Mi"
          volumeMounts:
            - name: app-config
              mountPath: /app/application/helpdesk-application/config/env/staging.json
              subPath: staging.json
