apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-central-1:556123534406:certificate/fc2a920e-1649-43fd-9742-3ac121338dab
    alb.ingress.kubernetes.io/load-balancer-attributes: |
      idle_timeout.timeout_seconds=1200,
      access_logs.s3.enabled=true,
      access_logs.s3.bucket=ss-production-alb-logs-eu,
      access_logs.s3.prefix=eu-ff-trusted-gateway-alb,
      deletion_protection.enabled=true
    alb.ingress.kubernetes.io/load-balancer-name: eu-ff-trusted-gateway-alb
    alb.ingress.kubernetes.io/target-node-labels: app=trusted-gateway
    alb.ingress.kubernetes.io/group.name: ss-internal-services
    alb.ingress.kubernetes.io/security-groups: sg-0cf60a2e7d2328b65
    alb.ingress.kubernetes.io/tags: CreatedBy=<EMAIL>,Team=Shared,Service=TrustedGateway
spec:
  ingressClassName: my-aws-ingress-class
  rules:
  - http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: haproxy-service
            port:
              number: 8080
