apiVersion: apps/v1
kind: Deployment
metadata:
  name: reputation-backend-application
  labels:
    app: reputation-backend-application
    application: reputation-backend
    group: reputation-backend-application
spec:
  selector:
    matchLabels:
      app: reputation-backend-application
      application: reputation-backend
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 100%
  template:
    metadata:
      labels:
        app: reputation-backend-application
        application: reputation-backend
        group: reputation-backend-application
    spec:
      nodeSelector: 
        app: reputation-backend-application
      containers:
      - name: reputation-backend-application
        image: ss-reputation-backend-application:latest
        imagePullPolicy: Always
        volumeMounts:
        - name: config-mount
          mountPath: /app/config/staging.json
          subPath: staging.json
        env:
        - name: NEW_RELIC_SPAN_EVENTS_MAX_SAMPLES_STORED
          value: "100"
        readinessProbe:
          httpGet:
            path: /status
            port: 8080
            httpHeaders:
            - name: x-probe
              value: READINESS
          initialDelaySeconds: 30
          timeoutSeconds: 20
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        livenessProbe:
          httpGet:
            path: /status
            port: 8080
            httpHeaders:
            - name: x-probe
              value: LIVENESS
          initialDelaySeconds: 40
          timeoutSeconds: 20
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        ports:
          - containerPort: 8080
        resources:
          requests:
            cpu: 1000m
            memory: 512Mi
          limits:
            memory: 1Gi
      volumes:
      - name: config-mount
        configMap:
          name: reputation-backend-config
          items:
          - key: staging.json
            path: staging.json
