apiVersion: apps/v1
kind: Deployment
metadata:
  name: ticket-backend-application
  labels:
    app: ticket-backend-application
    application: ticket-backend
    group: ticket-backend-application
spec:
  selector:
    matchLabels:
      app: ticket-backend-application
      application: ticket-backend
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: ticket-backend-application
        application: ticket-backend
        group: ticket-backend-application
    spec:
      nodeSelector:
        app: ticket-backend-application
      containers:
      - name: ticket-backend-application
        image: ss-ticket-backend-application:latest
        imagePullPolicy: Always
        volumeMounts:
        - name: config-mount
          mountPath: /app/config/staging.json
          subPath: staging.json
        env:
        - name: NEW_RELIC_SPAN_EVENTS_MAX_SAMPLES_STORED
          value: "100"
        readinessProbe:
          httpGet:
            path: /status
            port: 8080
            httpHeaders:
            - name: x-probe
              value: READINESS
          initialDelaySeconds: 30
          timeoutSeconds: 20
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        livenessProbe:
          httpGet:
            path: /status
            port: 8080
            httpHeaders:
            - name: x-probe
              value: LIVENESS
          initialDelaySeconds: 40
          timeoutSeconds: 20
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        ports:
          - containerPort: 8080
        resources:
          requests:
            cpu: 1000m
            memory: 512Mi
          limits:
            memory: 1Gi
      volumes:
      - name: config-mount
        configMap:
          name: ticket-backend-config
          items:
          - key: staging.json
            path: staging.json
