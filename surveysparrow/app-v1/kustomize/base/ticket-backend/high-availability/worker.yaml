apiVersion: apps/v1
kind: Deployment
metadata:
  name: ticket-backend-worker
  labels:
    app: ticket-backend-worker
    application: ticket-backend
    group: ticket-backend-worker
spec:
  selector:
    matchLabels:
      app: ticket-backend-worker
      application: ticket-backend
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: ticket-backend-worker
        application: ticket-backend
        group: ticket-backend-worker
    spec:
      nodeSelector: 
        app: ticket-backend-worker
      containers:
      - name: ticket-backend-worker
        image: ss-ticket-backend-worker:latest
        imagePullPolicy: Always
        volumeMounts:
        - name: config-mount
          mountPath: /app/config/production.json
          subPath: production.json
        env:
        - name: NEW_RELIC_SPAN_EVENTS_MAX_SAMPLES_STORED
          value: "100"
        readinessProbe:
          httpGet:
            path: /status
            port: 8080
            httpHeaders:
            - name: x-probe
              value: READINESS
          initialDelaySeconds: 30
          timeoutSeconds: 20
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        livenessProbe:
          httpGet:
            path: /status
            port: 8080
            httpHeaders:
            - name: x-probe
              value: LIVENESS
          initialDelaySeconds: 40
          timeoutSeconds: 20
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        ports:
          - containerPort: 8080
        resources:
          requests:
            cpu: 1000m
            memory: 2Gi
          limits:
            memory: 4Gi
      volumes:
      - name: config-mount
        configMap:
          name: ticket-backend-config
          items:
          - key: production.json
            path: production.json
