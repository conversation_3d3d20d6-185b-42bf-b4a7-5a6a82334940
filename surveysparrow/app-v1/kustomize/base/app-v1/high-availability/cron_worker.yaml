apiVersion: apps/v1
kind: Deployment
metadata:
  name: cron-worker
  labels:
    app: cron-worker
    group: app-v1-worker
spec:
  selector:
    matchLabels:
      app: cron-worker
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: cron-worker
        application: cron-worker
        group: app-v1-worker
    spec:
      containers:
      - name: worker
        image: us-east-1:latest
        imagePullPolicy: IfNotPresent
        volumeMounts:
        - name: config-mount
          mountPath: /app/config/production.json
          subPath: production.json
        env:
        - name: NEW_RELIC_SPAN_EVENTS_MAX_SAMPLES_STORED
          value: "100"
        readinessProbe:
          httpGet:
            path: /status
            port: 8080
            httpHeaders:
            - name: x-probe
              value: READINESS
          initialDelaySeconds: 25
          timeoutSeconds: 20
          periodSeconds: 10
          failureThreshold: 5
          successThreshold: 1
        livenessProbe:
          httpGet:
            path: /status
            port: 8080
            httpHeaders:
            - name: x-probe
              value: LIVENESS
          initialDelaySeconds: 25
          timeoutSeconds: 20
          periodSeconds: 10
          failureThreshold: 5
          successThreshold: 1
        ports:
          - containerPort: 8080
        resources:
          requests:
            cpu: 1000m
            memory: 4Gi
          limits:
            cpu: 2000m
            memory: 10Gi
      volumes:
      - name: config-mount
        configMap:
          name: config
          items:
          - key: production.json
            path: production.json
