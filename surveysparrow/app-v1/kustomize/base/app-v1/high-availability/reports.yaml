reportsVersion: apps/v1
kind: Deployment
metadata:
  name: reports
  labels:
    app: reports
    group: app-v1-application
spec:
  selector:
    matchLabels:
      app: reports
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: reports
        application: reports
        group: app-v1-application
    spec:
      containers:
      - name: reports
        image: us-east-1:latest
        imagePullPolicy: IfNotPresent
        volumeMounts:
        - name: config-mount
          mountPath: /app/config/production.json
          subPath: production.json
        env:
        - name: NEW_RELIC_SPAN_EVENTS_MAX_SAMPLES_STORED
          value: "100"
        - name: NODE_CONFIG
          value: '{"worker":false, "cronWorker":false,"layer":"reports"}'
        readinessProbe:
          httpGet:
            path: /status
            port: 8080
            httpHeaders:
            - name: x-probe
              value: READINESS
          initialDelaySeconds: 30
          timeoutSeconds: 20
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        livenessProbe:
          httpGet:
            path: /status
            port: 8080
            httpHeaders:
            - name: x-probe
              value: LIVENESS
          initialDelaySeconds: 40
          timeoutSeconds: 20
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        ports:
          - containerPort: 8080
        resources:
          requests:
            cpu: 1500m
            memory: 4Gi
          limits:
            memory: 4Gi
      volumes:
      - name: config-mount
        configMap:
          name: config
          items:
          - key: production.json
            path: production.json