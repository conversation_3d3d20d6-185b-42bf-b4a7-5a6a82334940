apiVersion: apps/v1
kind: Deployment
metadata:
  name: eui-backend-worker
  labels:
    app: eui-backend-worker
    application: eui-backend
    group: eui-backend-worker
spec:
  selector:
    matchLabels:
      app: eui-backend-worker
      application: eui-backend
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: eui-backend-worker
        application: eui-backend
        group: eui-backend-worker
    spec:
      nodeSelector: 
        app: eui-backend-worker
      containers:
      - name: eui-backend-worker
        image: ss-eui-backend-worker:latest
        imagePullPolicy: Always
        volumeMounts:
        - name: config-mount
          mountPath: /app/config/production.json
          subPath: production.json
        env:
        - name: NEW_RELIC_SPAN_EVENTS_MAX_SAMPLES_STORED
          value: "100"
        readinessProbe:
          httpGet:
            path: /status
            port: 8080
            httpHeaders:
            - name: x-probe
              value: READINESS
          initialDelaySeconds: 30
          timeoutSeconds: 20
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        livenessProbe:
          httpGet:
            path: /status
            port: 8080
            httpHeaders:
            - name: x-probe
              value: LIVENESS
          initialDelaySeconds: 40
          timeoutSeconds: 20
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        ports:
          - containerPort: 8080
        resources:
          requests:
              cpu: 1000m
              memory: 2Gi
          limits:
              memory: 4Gi
      volumes:
      - name: config-mount
        configMap:
          name: eui-backend-config
          items:
          - key: production.json
            path: production.json
