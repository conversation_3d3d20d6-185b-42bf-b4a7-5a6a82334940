apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../../../base/haproxy
- ../../../../../../ingress-base/staging/http-us-east-1
- *****************:surveysparrow/surveysparrow-staging-config.git//hangar/haproxy?ref=master
- amd_karpenter.yaml
- arm_karpenter.yaml


images:
- name: haproxy
  newName: 713859105457.dkr.ecr.us-east-1.amazonaws.com/docker-hub/library/haproxy
  newTag: "3.0"

patches:
- path: ingress.yaml
- path: node_selector.yaml
labels:
- includeSelectors: true
  pairs:
    app: haproxy
    application: haproxy
